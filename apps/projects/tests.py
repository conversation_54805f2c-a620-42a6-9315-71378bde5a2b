from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model
from datetime import timedelta
from django.utils import timezone
from .models import Project, ProjectTeamMember, HeadcountRequest
from apps.team.models import TeamMember, AIRole

User = get_user_model()


class ProjectModelTest(TestCase):
    """Test cases for the Project model."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            company_name='Test Company'
        )

    def test_create_project(self):
        """Test creating a project with valid data."""
        project = Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH',
            estimated_duration=timedelta(days=30),
            budget=10000.00
        )

        self.assertEqual(project.title, 'Test Project')
        self.assertEqual(project.owner, self.user)
        self.assertEqual(project.status, 'PLANNING')  # Default status
        self.assertEqual(project.priority, 'HIGH')
        self.assertTrue(project.id)  # UUID should be generated

    def test_project_string_representation(self):
        """Test the string representation of project."""
        project = Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH'
        )
        self.assertEqual(str(project), 'Test Project')

    def test_project_progress_percentage(self):
        """Test the progress percentage calculation."""
        project = Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH'
        )
        # Initially should be 0
        self.assertEqual(project.progress_percentage, 0.0)


class ProjectAPITest(APITestCase):
    """Test cases for Project API endpoints."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            company_name='Test Company'
        )
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        self.project_data = {
            'title': 'Test Project',
            'description': 'A test project description',
            'requirements': 'Test project requirements',
            'priority': 'HIGH',
            'estimated_duration': '30 00:00:00',  # 30 days
            'budget': '10000.00'
        }

    def test_create_project_success(self):
        """Test successful project creation."""
        url = reverse('projects:project-list')
        response = self.client.post(url, self.project_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Project.objects.count(), 1)
        project = Project.objects.first()
        self.assertEqual(project.title, self.project_data['title'])
        self.assertEqual(project.owner, self.user)

    def test_list_projects(self):
        """Test listing projects."""
        # Create a project
        Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH'
        )

        url = reverse('projects:project-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(len(response.data['results']), 1)

    def test_get_project_detail(self):
        """Test getting project details."""
        project = Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH'
        )

        url = reverse('projects:project-detail', kwargs={'pk': project.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], project.title)
        self.assertEqual(response.data['id'], str(project.id))

    def test_update_project(self):
        """Test updating a project."""
        project = Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH'
        )

        url = reverse('projects:project-detail', kwargs={'pk': project.id})
        update_data = {'title': 'Updated Project Title'}
        response = self.client.patch(url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        project.refresh_from_db()
        self.assertEqual(project.title, 'Updated Project Title')

    def test_delete_project(self):
        """Test deleting a project."""
        project = Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH'
        )

        url = reverse('projects:project-detail', kwargs={'pk': project.id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Project.objects.count(), 0)

    def test_unauthorized_access(self):
        """Test unauthorized access to projects."""
        self.client.credentials()  # Remove authentication

        url = reverse('projects:project-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
