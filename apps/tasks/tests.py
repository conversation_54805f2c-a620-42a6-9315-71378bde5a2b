from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model
from datetime import timedelta
from django.utils import timezone
from .models import Task, TaskAssignment, WorkflowState
from apps.projects.models import Project
from apps.team.models import TeamMember, AIRole

User = get_user_model()


class TaskModelTest(TestCase):
    """Test cases for the Task model."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            company_name='Test Company'
        )
        self.project = Project.objects.create(
            owner=self.user,
            title='Test Project',
            description='A test project',
            requirements='Test requirements',
            priority='HIGH'
        )

    def test_create_task(self):
        """Test creating a task with valid data."""
        task = Task.objects.create(
            project=self.project,
            title='Test Task',
            description='A test task',
            priority='HIGH',
            estimated_hours=8.0,
            complexity_score=5
        )

        self.assertEqual(task.title, 'Test Task')
        self.assertEqual(task.project, self.project)
        self.assertEqual(task.status, 'TODO')  # Default status
        self.assertEqual(task.priority, 'HIGH')
        self.assertEqual(task.estimated_hours, 8.0)
        self.assertTrue(task.id)  # UUID should be generated

    def test_task_string_representation(self):
        """Test the string representation of task."""
        task = Task.objects.create(
            project=self.project,
            title='Test Task',
            description='A test task',
            priority='HIGH',
            estimated_hours=8.0
        )
        self.assertEqual(str(task), 'Test Task')

    def test_task_progress_percentage(self):
        """Test the progress percentage calculation."""
        task = Task.objects.create(
            project=self.project,
            title='Test Task',
            description='A test task',
            priority='HIGH',
            estimated_hours=10.0
        )

        # Initially should be 0
        self.assertEqual(task.progress_percentage, 0)

        # Set actual hours to 5 (50% of estimated)
        task.actual_hours = 5.0
        task.save()
        self.assertEqual(task.progress_percentage, 50.0)

        # Set actual hours to more than estimated
        task.actual_hours = 15.0
        task.save()
        self.assertEqual(task.progress_percentage, 100)  # Capped at 100%

    def test_task_is_overdue(self):
        """Test the is_overdue property."""
        # Task without due date should not be overdue
        task = Task.objects.create(
            project=self.project,
            title='Test Task',
            description='A test task',
            priority='HIGH',
            estimated_hours=8.0
        )
        self.assertFalse(task.is_overdue)

        # Task with future due date should not be overdue
        task.due_date = timezone.now() + timedelta(days=1)
        task.save()
        self.assertFalse(task.is_overdue)

        # Task with past due date should be overdue
        task.due_date = timezone.now() - timedelta(days=1)
        task.save()
        self.assertTrue(task.is_overdue)

        # Completed task should not be overdue even if past due date
        task.status = 'COMPLETED'
        task.save()
        self.assertFalse(task.is_overdue)
