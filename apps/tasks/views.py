from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Count, Q, Avg, Sum
from .models import Task, TaskAssignment, WorkflowState
from .serializers import (
    TaskListSerializer, TaskDetailSerializer, TaskCreateSerializer,
    TaskAssignmentSerializer, TaskStatusUpdateSerializer
)
from apps.team.models import TeamMember


class TaskViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing tasks with CRUD operations and custom actions.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Filter tasks by projects owned by the current user
        return Task.objects.filter(
            project__owner=self.request.user
        ).select_related('project', 'parent_task').order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return TaskListSerializer
        elif self.action == 'create':
            return TaskCreateSerializer
        return TaskDetailSerializer

    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """
        Assign task to a team member.
        """
        task = self.get_object()
        serializer = TaskAssignmentSerializer(data=request.data)

        if serializer.is_valid():
            # Check if task is already assigned
            existing_assignment = TaskAssignment.objects.filter(
                task=task,
                status__in=['ASSIGNED', 'ACCEPTED', 'IN_PROGRESS']
            ).first()

            if existing_assignment:
                return Response(
                    {'error': 'Task is already assigned'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            serializer.save(task=task, assigned_by='CEO')
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """
        Update task status and progress.
        """
        task = self.get_object()
        serializer = TaskStatusUpdateSerializer(
            data=request.data,
            context={'task': task}
        )

        if serializer.is_valid():
            new_status = serializer.validated_data['status']
            notes = serializer.validated_data.get('notes', '')
            actual_hours = serializer.validated_data.get('actual_hours')

            task.status = new_status
            if actual_hours is not None:
                task.actual_hours = actual_hours

            if new_status == 'COMPLETED':
                task.completion_date = timezone.now()
            elif new_status == 'IN_PROGRESS' and not task.start_date:
                task.start_date = timezone.now()

            task.save()

            # Update assignment status if exists
            assignment = TaskAssignment.objects.filter(
                task=task,
                status__in=['ASSIGNED', 'ACCEPTED', 'IN_PROGRESS']
            ).first()

            if assignment:
                if new_status == 'COMPLETED':
                    assignment.status = 'COMPLETED'
                    assignment.completed_at = timezone.now()
                elif new_status == 'IN_PROGRESS':
                    assignment.status = 'IN_PROGRESS'
                    if not assignment.started_at:
                        assignment.started_at = timezone.now()

                if notes:
                    assignment.notes = notes
                assignment.save()

            return Response(TaskDetailSerializer(task).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        Get task statistics for the current user's projects.
        """
        tasks = self.get_queryset()

        # Basic task statistics
        total_tasks = tasks.count()
        status_counts = tasks.values('status').annotate(count=Count('id'))
        priority_counts = tasks.values('priority').annotate(count=Count('id'))

        # Time tracking statistics
        total_estimated_hours = tasks.aggregate(total=Sum('estimated_hours'))['total'] or 0
        total_actual_hours = tasks.aggregate(total=Sum('actual_hours'))['total'] or 0
        avg_complexity = tasks.aggregate(avg=Avg('complexity_score'))['avg'] or 0

        # Completion statistics
        completed_tasks = tasks.filter(status='COMPLETED').count()
        overdue_tasks = tasks.filter(
            due_date__lt=timezone.now(),
            status__in=['TODO', 'IN_PROGRESS']
        ).count()

        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

        # Assignment statistics
        assigned_tasks = TaskAssignment.objects.filter(
            task__in=tasks,
            status__in=['ASSIGNED', 'ACCEPTED', 'IN_PROGRESS', 'COMPLETED']
        ).count()

        assignment_rate = (assigned_tasks / total_tasks * 100) if total_tasks > 0 else 0

        return Response({
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'overdue_tasks': overdue_tasks,
            'assigned_tasks': assigned_tasks,
            'completion_rate': round(completion_rate, 2),
            'assignment_rate': round(assignment_rate, 2),
            'total_estimated_hours': total_estimated_hours,
            'total_actual_hours': total_actual_hours,
            'average_complexity': round(avg_complexity, 2) if avg_complexity else 0,
            'status_distribution': {item['status']: item['count'] for item in status_counts},
            'priority_distribution': {item['priority']: item['count'] for item in priority_counts}
        })

    @action(detail=False, methods=['post'])
    def bulk_assign(self, request):
        """
        Assign multiple tasks to team members in bulk.
        """
        task_assignments = request.data.get('assignments', [])
        if not task_assignments:
            return Response({'error': 'No assignments provided'}, status=status.HTTP_400_BAD_REQUEST)

        created_assignments = []
        errors = []

        for assignment_data in task_assignments:
            task_id = assignment_data.get('task_id')
            team_member_id = assignment_data.get('team_member_id')
            assignment_reason = assignment_data.get('assignment_reason', '')

            try:
                task = Task.objects.get(id=task_id, project__owner=request.user)
                team_member = TeamMember.objects.get(id=team_member_id)

                # Check if task is already assigned
                existing_assignment = TaskAssignment.objects.filter(
                    task=task,
                    status__in=['ASSIGNED', 'ACCEPTED', 'IN_PROGRESS']
                ).first()

                if existing_assignment:
                    errors.append({
                        'task_id': task_id,
                        'error': 'Task is already assigned'
                    })
                    continue

                assignment = TaskAssignment.objects.create(
                    task=task,
                    team_member=team_member,
                    assigned_by='CEO',
                    assignment_reason=assignment_reason,
                    status='ASSIGNED'
                )
                created_assignments.append(TaskAssignmentSerializer(assignment).data)

            except Task.DoesNotExist:
                errors.append({
                    'task_id': task_id,
                    'error': 'Task not found or not owned by user'
                })
            except TeamMember.DoesNotExist:
                errors.append({
                    'task_id': task_id,
                    'error': 'Team member not found'
                })
            except Exception as e:
                errors.append({
                    'task_id': task_id,
                    'error': str(e)
                })

        return Response({
            'created_assignments': created_assignments,
            'errors': errors,
            'success_count': len(created_assignments),
            'error_count': len(errors)
        })

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """
        Get all overdue tasks.
        """
        overdue_tasks = self.get_queryset().filter(
            due_date__lt=timezone.now(),
            status__in=['TODO', 'IN_PROGRESS']
        ).order_by('due_date')

        serializer = TaskListSerializer(overdue_tasks, many=True)
        return Response(serializer.data)
