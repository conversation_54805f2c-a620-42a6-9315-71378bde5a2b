"""
URL configuration for AI Partner backend project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/auth/', include('apps.authentication.urls')),
    path('api/', include('apps.projects.urls')),
    path('api/', include('apps.team.urls')),
    path('api/', include('apps.tasks.urls')),
    path('api/ai/', include('apps.ai_service.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
