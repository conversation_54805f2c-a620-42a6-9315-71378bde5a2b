{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.3", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@playwright/test": "^1.53.2", "@vitejs/plugin-vue": "^5.2.4", "vite": "^5.4.19", "vite-plugin-vue-devtools": "^7.7.7"}}