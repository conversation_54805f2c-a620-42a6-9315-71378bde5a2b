<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>AI Partner</h1>
        <p>Create Your Software Company Account</p>
      </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        @submit.prevent="handleRegister"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="first_name">
              <el-input
                v-model="registerForm.first_name"
                placeholder="First Name"
                size="large"
                prefix-icon="User"
                data-testid="first-name-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="last_name">
              <el-input
                v-model="registerForm.last_name"
                placeholder="Last Name"
                size="large"
                prefix-icon="User"
                data-testid="last-name-input"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="Email Address"
            size="large"
            prefix-icon="Message"
            data-testid="email-input"
          />
        </el-form-item>

        <el-form-item prop="company_name">
          <el-input
            v-model="registerForm.company_name"
            placeholder="Company Name"
            size="large"
            prefix-icon="OfficeBuilding"
            data-testid="company-name-input"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="Password"
            size="large"
            prefix-icon="Lock"
            show-password
            data-testid="password-input"
          />
        </el-form-item>

        <el-form-item prop="password_confirm">
          <el-input
            v-model="registerForm.password_confirm"
            type="password"
            placeholder="Confirm Password"
            size="large"
            prefix-icon="Lock"
            show-password
            data-testid="confirm-password-input"
            @keyup.enter="handleRegister"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="register-button"
            :loading="isLoading"
            data-testid="register-button"
            @click="handleRegister"
          >
            {{ isLoading ? 'Creating Account...' : 'Create Account' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-link">
        <el-divider>Already have an account?</el-divider>
        <el-button type="text" @click="$router.push('/login')">
          Sign In
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import { ElMessage } from 'element-plus'
import api from '../services/api.js'

const router = useRouter()
const authStore = useAuthStore()
const registerFormRef = ref()
const isLoading = ref(false)

const registerForm = reactive({
  first_name: '',
  last_name: '',
  email: '',
  company_name: '',
  password: '',
  password_confirm: ''
})

const validatePasswordConfirm = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('Passwords do not match'))
  } else {
    callback()
  }
}

const registerRules = {
  first_name: [
    { required: true, message: 'Please enter your first name', trigger: 'blur' },
    { min: 2, message: 'First name must be at least 2 characters', trigger: 'blur' }
  ],
  last_name: [
    { required: true, message: 'Please enter your last name', trigger: 'blur' },
    { min: 2, message: 'Last name must be at least 2 characters', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Please enter your email', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email', trigger: 'blur' }
  ],
  company_name: [
    { required: true, message: 'Please enter your company name', trigger: 'blur' },
    { min: 2, message: 'Company name must be at least 2 characters', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter your password', trigger: 'blur' },
    { min: 8, message: 'Password must be at least 8 characters', trigger: 'blur' }
  ],
  password_confirm: [
    { required: true, message: 'Please confirm your password', trigger: 'blur' },
    { validator: validatePasswordConfirm, trigger: 'blur' }
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      isLoading.value = true
      
      try {
        const response = await api.post('/auth/register/', registerForm)
        const { user, token } = response.data
        
        // Store authentication data
        authStore.user = user
        authStore.token = token
        localStorage.setItem('token', token)
        api.defaults.headers.common['Authorization'] = `Token ${token}`
        
        ElMessage.success('Account created successfully!')
        router.push('/')
      } catch (error) {
        console.error('Registration error:', error)
        
        if (error.response?.data) {
          const errors = error.response.data
          
          // Handle field-specific errors
          Object.keys(errors).forEach(field => {
            if (Array.isArray(errors[field])) {
              ElMessage.error(`${field}: ${errors[field][0]}`)
            }
          })
          
          // Handle non-field errors
          if (errors.non_field_errors) {
            ElMessage.error(errors.non_field_errors[0])
          }
        } else {
          ElMessage.error('Registration failed. Please try again.')
        }
      } finally {
        isLoading.value = false
      }
    }
  })
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  color: #409EFF;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: bold;
}

.register-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.register-form {
  margin-bottom: 20px;
}

.register-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
  font-weight: bold;
}

.login-link {
  text-align: center;
}

.login-link .el-button {
  color: #409EFF;
  font-size: 16px;
}

.login-link .el-button:hover {
  color: #66b1ff;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__inner) {
  height: 45px;
  font-size: 14px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
