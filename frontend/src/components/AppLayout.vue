<template>
  <el-container class="app-container">
    <!-- Sidebar Navigation -->
    <el-aside width="250px" class="sidebar">
      <div class="logo-container">
        <h2>AI Partner</h2>
        <p>Software Company Simulation</p>
      </div>
      
      <el-menu
        :default-active="$route.path"
        class="sidebar-menu"
        router
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        data-testid="main-navigation"
      >
        <el-menu-item index="/">
          <el-icon><House /></el-icon>
          <span>Dashboard</span>
        </el-menu-item>
        
        <el-menu-item index="/projects">
          <el-icon><Folder /></el-icon>
          <span>Projects</span>
        </el-menu-item>
        
        <el-menu-item index="/tasks">
          <el-icon><List /></el-icon>
          <span>Tasks</span>
        </el-menu-item>
        
        <el-menu-item index="/team">
          <el-icon><User /></el-icon>
          <span>Team Management</span>
        </el-menu-item>
        
        <el-menu-item index="/headcount-requests">
          <el-icon><Plus /></el-icon>
          <span>Headcount Requests</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- Main Content Area -->
    <el-container>
      <!-- Header -->
      <el-header class="header">
        <div class="header-left">
          <h3>{{ getPageTitle() }}</h3>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleUserAction" data-testid="user-menu">
            <span class="user-dropdown">
              <el-icon><Avatar /></el-icon>
              <span data-testid="user-name">{{ authStore.userInfo?.full_name || 'User' }}</span>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">Profile</el-dropdown-item>
                <el-dropdown-item command="logout" divided data-testid="logout-button">Logout</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- Main Content -->
      <el-main class="main-content" data-testid="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import {
  House,
  Folder,
  List,
  User,
  Plus,
  Avatar,
  ArrowDown
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const getPageTitle = () => {
  const titles = {
    '/': 'Dashboard',
    '/projects': 'Projects',
    '/tasks': 'Tasks',
    '/team': 'Team Management',
    '/headcount-requests': 'Headcount Requests'
  }
  return titles[route.path] || 'AI Partner'
}

const handleUserAction = (command) => {
  if (command === 'logout') {
    authStore.logout()
    router.push('/login')
  } else if (command === 'profile') {
    // Handle profile action
    console.log('Profile clicked')
  }
}
</script>

<style scoped>
.app-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  color: white;
}

.logo-container {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.logo-container h2 {
  margin: 0 0 5px 0;
  color: #409EFF;
  font-size: 20px;
}

.logo-container p {
  margin: 0;
  color: #bfcbd9;
  font-size: 12px;
}

.sidebar-menu {
  border: none;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h3 {
  margin: 0;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
}

.user-dropdown:hover {
  color: #409EFF;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}
</style>
