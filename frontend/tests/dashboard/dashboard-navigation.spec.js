import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers.js';

test.describe('Dashboard and Navigation', () => {
  let helpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.login();
  });

  test.describe('Dashboard Data Display', () => {
    test('should display dashboard statistics', async ({ page }) => {
      await helpers.navigateTo('/');
      
      // Wait for dashboard to load
      await helpers.waitForLoading();
      
      // Check if main dashboard elements are visible
      await expect(page.locator('[data-testid="dashboard-stats"]')).toBeVisible();
      await expect(page.locator('[data-testid="projects-stat"]')).toBeVisible();
      await expect(page.locator('[data-testid="team-members-stat"]')).toBeVisible();
      await expect(page.locator('[data-testid="headcount-requests-stat"]')).toBeVisible();
      
      // Verify stats contain numbers
      await expect(page.locator('[data-testid="projects-count"]')).toContainText(/\d+/);
      await expect(page.locator('[data-testid="team-members-count"]')).toContainText(/\d+/);
      await expect(page.locator('[data-testid="headcount-requests-count"]')).toContainText(/\d+/);
    });

    test('should display recent projects section', async ({ page }) => {
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Check recent projects section
      await expect(page.locator('[data-testid="recent-projects"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-projects-title"]')).toContainText('Recent Projects');
      
      // Verify at least one project is shown or empty state
      const projectCards = page.locator('[data-testid^="recent-project-"]');
      const count = await projectCards.count();
      
      if (count > 0) {
        // Check project card elements
        await expect(projectCards.first().locator('[data-testid="project-title"]')).toBeVisible();
        await expect(projectCards.first().locator('[data-testid="project-status"]')).toBeVisible();
      } else {
        await expect(page.locator('[data-testid="no-projects-message"]')).toBeVisible();
      }
    });

    test('should display team members section', async ({ page }) => {
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Check team members section
      await expect(page.locator('[data-testid="team-members-section"]')).toBeVisible();
      await expect(page.locator('[data-testid="team-members-title"]')).toContainText('Team Members');
      
      // Verify team member cards
      const memberCards = page.locator('[data-testid^="team-member-"]');
      const count = await memberCards.count();
      
      if (count > 0) {
        await expect(memberCards.first().locator('[data-testid="member-name"]')).toBeVisible();
        await expect(memberCards.first().locator('[data-testid="member-role"]')).toBeVisible();
      }
    });

    test('should display activity feed', async ({ page }) => {
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Check activity feed
      await expect(page.locator('[data-testid="activity-feed"]')).toBeVisible();
      await expect(page.locator('[data-testid="activity-feed-title"]')).toContainText('Recent Activity');
      
      // Check for activity items or empty state
      const activityItems = page.locator('[data-testid^="activity-item-"]');
      const count = await activityItems.count();
      
      if (count > 0) {
        await expect(activityItems.first().locator('[data-testid="activity-description"]')).toBeVisible();
        await expect(activityItems.first().locator('[data-testid="activity-timestamp"]')).toBeVisible();
      } else {
        await expect(page.locator('[data-testid="no-activity-message"]')).toBeVisible();
      }
    });

    test('should refresh dashboard data', async ({ page }) => {
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Get initial project count
      const initialCount = await page.locator('[data-testid="projects-count"]').textContent();
      
      // Refresh dashboard
      await page.click('[data-testid="refresh-dashboard-button"]');
      await helpers.waitForLoading();
      
      // Verify data is still displayed (may be same or different)
      await expect(page.locator('[data-testid="projects-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="projects-count"]')).toContainText(/\d+/);
    });
  });

  test.describe('Navigation Between Pages', () => {
    test('should navigate to all main pages from sidebar', async ({ page }) => {
      const navigationItems = [
        { path: '/', testId: 'dashboard-nav', expectedUrl: '/', title: 'Dashboard' },
        { path: '/projects', testId: 'projects-nav', expectedUrl: '/projects', title: 'Projects' },
        { path: '/tasks', testId: 'tasks-nav', expectedUrl: '/tasks', title: 'Tasks' },
        { path: '/team', testId: 'team-nav', expectedUrl: '/team', title: 'Team Management' },
        { path: '/headcount-requests', testId: 'headcount-nav', expectedUrl: '/headcount-requests', title: 'Headcount Requests' }
      ];
      
      for (const item of navigationItems) {
        // Click navigation item
        await page.click(`[data-testid="${item.testId}"]`);
        
        // Verify URL and page content
        await expect(page).toHaveURL(item.expectedUrl);
        await expect(page.locator('h3')).toContainText(item.title);
        
        // Wait for page to load
        await helpers.waitForLoading();
      }
    });

    test('should highlight active navigation item', async ({ page }) => {
      const pages = ['/', '/projects', '/tasks', '/team', '/headcount-requests'];
      
      for (const pagePath of pages) {
        await helpers.navigateTo(pagePath);
        
        // Check that the corresponding menu item is active
        const activeMenuItem = page.locator('.el-menu-item.is-active');
        await expect(activeMenuItem).toBeVisible();
      }
    });

    test('should navigate using browser back and forward buttons', async ({ page }) => {
      // Navigate through several pages
      await helpers.navigateTo('/');
      await helpers.navigateTo('/projects');
      await helpers.navigateTo('/tasks');
      
      // Use browser back button
      await page.goBack();
      await expect(page).toHaveURL('/projects');
      
      await page.goBack();
      await expect(page).toHaveURL('/');
      
      // Use browser forward button
      await page.goForward();
      await expect(page).toHaveURL('/projects');
    });

    test('should maintain navigation state after page refresh', async ({ page }) => {
      await helpers.navigateTo('/projects');
      
      // Refresh the page
      await page.reload();
      
      // Verify we're still on the projects page
      await expect(page).toHaveURL('/projects');
      await expect(page.locator('h3')).toContainText('Projects');
    });
  });

  test.describe('Responsive Layout Verification', () => {
    test('should display correctly on mobile devices', async ({ page }) => {
      const results = await helpers.checkResponsiveLayout([
        { name: 'mobile', width: 375, height: 667 }
      ]);
      
      // Verify mobile layout
      expect(results.mobile.navigation).toBeTruthy();
      expect(results.mobile.content).toBeTruthy();
      
      // Check if mobile menu toggle is visible
      await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
      
      // Test mobile menu functionality
      await page.click('[data-testid="mobile-menu-toggle"]');
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    });

    test('should display correctly on tablet devices', async ({ page }) => {
      const results = await helpers.checkResponsiveLayout([
        { name: 'tablet', width: 768, height: 1024 }
      ]);
      
      // Verify tablet layout
      expect(results.tablet.navigation).toBeTruthy();
      expect(results.tablet.content).toBeTruthy();
      
      // Check that sidebar is visible on tablet
      await expect(page.locator('[data-testid="main-navigation"]')).toBeVisible();
    });

    test('should display correctly on desktop', async ({ page }) => {
      const results = await helpers.checkResponsiveLayout([
        { name: 'desktop', width: 1920, height: 1080 }
      ]);
      
      // Verify desktop layout
      expect(results.desktop.navigation).toBeTruthy();
      expect(results.desktop.content).toBeTruthy();
      
      // Check that all desktop elements are properly displayed
      await expect(page.locator('[data-testid="main-navigation"]')).toBeVisible();
      await expect(page.locator('[data-testid="main-content"]')).toBeVisible();
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    });

    test('should handle responsive dashboard layout', async ({ page }) => {
      await helpers.navigateTo('/');
      
      // Test different screen sizes
      const breakpoints = [
        { width: 375, height: 667 },   // Mobile
        { width: 768, height: 1024 },  // Tablet
        { width: 1200, height: 800 },  // Desktop
        { width: 1920, height: 1080 }  // Large Desktop
      ];
      
      for (const breakpoint of breakpoints) {
        await page.setViewportSize(breakpoint);
        await page.waitForTimeout(500); // Allow layout to adjust
        
        // Verify dashboard elements are still visible and properly arranged
        await expect(page.locator('[data-testid="dashboard-stats"]')).toBeVisible();
        
        // Check if stats are properly stacked on smaller screens
        if (breakpoint.width < 768) {
          // On mobile, stats should stack vertically
          const statsContainer = page.locator('[data-testid="dashboard-stats"]');
          const boundingBox = await statsContainer.boundingBox();
          expect(boundingBox.height).toBeGreaterThan(200); // Indicates vertical stacking
        }
      }
    });
  });

  test.describe('Page Loading and Performance', () => {
    test('should load dashboard within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      const loadTime = Date.now() - startTime;
      
      // Dashboard should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
      
      // Verify all critical elements are loaded
      await expect(page.locator('[data-testid="dashboard-stats"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-projects"]')).toBeVisible();
    });

    test('should handle loading states gracefully', async ({ page }) => {
      await helpers.navigateTo('/');
      
      // Check if loading indicators appear and disappear
      const loadingSpinner = page.locator('[data-testid="loading-spinner"]');
      
      // Loading spinner should not be visible after page loads
      await expect(loadingSpinner).not.toBeVisible();
      
      // Content should be visible
      await expect(page.locator('[data-testid="dashboard-stats"]')).toBeVisible();
    });

    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());
      
      await helpers.navigateTo('/');
      
      // Check for error handling
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      
      // Or check for retry mechanism
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
    });
  });

  test.describe('Search and Quick Actions', () => {
    test('should perform global search', async ({ page }) => {
      await helpers.navigateTo('/');
      
      // Check if global search is available
      if (await helpers.isVisible('[data-testid="global-search"]')) {
        await page.fill('[data-testid="global-search"]', 'test');
        await page.press('[data-testid="global-search"]', 'Enter');
        
        // Verify search results page or dropdown
        await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
      }
    });

    test('should access quick actions from dashboard', async ({ page }) => {
      await helpers.navigateTo('/');
      
      // Check for quick action buttons
      const quickActions = [
        '[data-testid="quick-create-project"]',
        '[data-testid="quick-create-task"]',
        '[data-testid="quick-add-member"]'
      ];
      
      for (const action of quickActions) {
        if (await helpers.isVisible(action)) {
          await expect(page.locator(action)).toBeVisible();
        }
      }
    });
  });
});
