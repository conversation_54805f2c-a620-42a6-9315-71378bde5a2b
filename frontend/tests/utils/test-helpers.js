import { expect } from '@playwright/test';

/**
 * Test utilities for AI Partner application
 */

export class TestHelpers {
  constructor(page) {
    this.page = page;
  }

  /**
   * Login with test credentials
   */
  async login(email = '<EMAIL>', password = 'testpass123') {
    await this.page.goto('/login');
    await this.page.fill('[data-testid="email-input"]', email);
    await this.page.fill('[data-testid="password-input"]', password);
    await this.page.click('[data-testid="login-button"]');
    
    // Wait for successful login redirect
    await this.page.waitForURL('/');
    await expect(this.page.locator('[data-testid="user-menu"]')).toBeVisible();
  }

  /**
   * Register a new user account
   */
  async register(userData = {}) {
    const defaultData = {
      firstName: 'Test',
      lastName: 'User',
      email: `test${Date.now()}@example.com`,
      companyName: 'Test Company',
      password: 'testpass123',
      confirmPassword: 'testpass123'
    };
    
    const data = { ...defaultData, ...userData };
    
    await this.page.goto('/register');
    await this.page.fill('[data-testid="first-name-input"]', data.firstName);
    await this.page.fill('[data-testid="last-name-input"]', data.lastName);
    await this.page.fill('[data-testid="email-input"]', data.email);
    await this.page.fill('[data-testid="company-name-input"]', data.companyName);
    await this.page.fill('[data-testid="password-input"]', data.password);
    await this.page.fill('[data-testid="confirm-password-input"]', data.confirmPassword);
    await this.page.click('[data-testid="register-button"]');
    
    // Wait for successful registration redirect
    await this.page.waitForURL('/');
    await expect(this.page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    return data;
  }

  /**
   * Logout current user
   */
  async logout() {
    await this.page.click('[data-testid="user-menu"]');
    await this.page.click('[data-testid="logout-button"]');
    await this.page.waitForURL('/login');
  }

  /**
   * Navigate to a specific page
   */
  async navigateTo(path) {
    await this.page.goto(path);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Wait for API response
   */
  async waitForApiResponse(urlPattern, method = 'GET') {
    return await this.page.waitForResponse(response => 
      response.url().includes(urlPattern) && response.request().method() === method
    );
  }

  /**
   * Create a test project
   */
  async createProject(projectData = {}) {
    const defaultData = {
      title: `Test Project ${Date.now()}`,
      description: 'A test project for automated testing',
      requirements: 'Test requirements for the project',
      priority: 'HIGH',
      estimatedDuration: '30',
      budget: '10000'
    };
    
    const data = { ...defaultData, ...projectData };
    
    await this.navigateTo('/projects');
    await this.page.click('[data-testid="create-project-button"]');
    
    // Fill project form
    await this.page.fill('[data-testid="project-title-input"]', data.title);
    await this.page.fill('[data-testid="project-description-input"]', data.description);
    await this.page.fill('[data-testid="project-requirements-input"]', data.requirements);
    await this.page.selectOption('[data-testid="project-priority-select"]', data.priority);
    await this.page.fill('[data-testid="project-duration-input"]', data.estimatedDuration);
    await this.page.fill('[data-testid="project-budget-input"]', data.budget);
    
    await this.page.click('[data-testid="save-project-button"]');
    
    // Wait for project creation
    await this.waitForApiResponse('/api/projects/', 'POST');
    
    return data;
  }

  /**
   * Create a test task
   */
  async createTask(taskData = {}) {
    const defaultData = {
      title: `Test Task ${Date.now()}`,
      description: 'A test task for automated testing',
      priority: 'MEDIUM',
      estimatedHours: '8'
    };
    
    const data = { ...defaultData, ...taskData };
    
    await this.navigateTo('/tasks');
    await this.page.click('[data-testid="create-task-button"]');
    
    // Fill task form
    await this.page.fill('[data-testid="task-title-input"]', data.title);
    await this.page.fill('[data-testid="task-description-input"]', data.description);
    await this.page.selectOption('[data-testid="task-priority-select"]', data.priority);
    await this.page.fill('[data-testid="task-hours-input"]', data.estimatedHours);
    
    if (data.assignee) {
      await this.page.selectOption('[data-testid="task-assignee-select"]', data.assignee);
    }
    
    await this.page.click('[data-testid="save-task-button"]');
    
    // Wait for task creation
    await this.waitForApiResponse('/api/tasks/', 'POST');
    
    return data;
  }

  /**
   * Check if element is visible with timeout
   */
  async isVisible(selector, timeout = 5000) {
    try {
      await this.page.waitForSelector(selector, { timeout, state: 'visible' });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check responsive layout
   */
  async checkResponsiveLayout(breakpoints = [
    { name: 'mobile', width: 375, height: 667 },
    { name: 'tablet', width: 768, height: 1024 },
    { name: 'desktop', width: 1920, height: 1080 }
  ]) {
    const results = {};
    
    for (const breakpoint of breakpoints) {
      await this.page.setViewportSize({ 
        width: breakpoint.width, 
        height: breakpoint.height 
      });
      
      // Wait for layout to adjust
      await this.page.waitForTimeout(500);
      
      // Check if main navigation is accessible
      const navVisible = await this.isVisible('[data-testid="main-navigation"]');
      const contentVisible = await this.isVisible('[data-testid="main-content"]');
      
      results[breakpoint.name] = {
        navigation: navVisible,
        content: contentVisible,
        viewport: { width: breakpoint.width, height: breakpoint.height }
      };
    }
    
    return results;
  }

  /**
   * Take screenshot with timestamp
   */
  async takeScreenshot(name) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await this.page.screenshot({ 
      path: `tests/screenshots/${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Wait for loading to complete
   */
  async waitForLoading() {
    // Wait for any loading spinners to disappear
    await this.page.waitForSelector('[data-testid="loading-spinner"]', { 
      state: 'hidden', 
      timeout: 10000 
    }).catch(() => {
      // Ignore if no loading spinner exists
    });
    
    // Wait for network to be idle
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Check for console errors
   */
  async getConsoleErrors() {
    const errors = [];
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    return errors;
  }
}

/**
 * API Test Helpers
 */
export class ApiHelpers {
  constructor(request) {
    this.request = request;
    this.baseURL = 'http://localhost:8000/api';
    this.token = null;
  }

  /**
   * Login and get authentication token
   */
  async login(email = '<EMAIL>', password = 'testpass123') {
    const response = await this.request.post(`${this.baseURL}/auth/login/`, {
      data: { email, password }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    this.token = data.token;
    return data;
  }

  /**
   * Make authenticated API request
   */
  async authenticatedRequest(method, endpoint, data = null) {
    const options = {
      headers: {
        'Authorization': `Token ${this.token}`,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      options.data = data;
    }
    
    return await this.request[method.toLowerCase()](`${this.baseURL}${endpoint}`, options);
  }

  /**
   * Create test data via API
   */
  async createTestProject(projectData = {}) {
    const defaultData = {
      title: `API Test Project ${Date.now()}`,
      description: 'Project created via API for testing',
      requirements: 'API test requirements',
      priority: 'HIGH',
      estimated_duration: '30 00:00:00',
      budget: '10000.00'
    };
    
    const data = { ...defaultData, ...projectData };
    return await this.authenticatedRequest('POST', '/projects/', data);
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    // This would implement cleanup logic for test data
    // For now, we'll rely on database reset between test runs
  }
}
