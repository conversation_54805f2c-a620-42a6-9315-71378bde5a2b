# AI Partner E2E Testing with <PERSON>wright

This directory contains comprehensive end-to-end tests for the AI Partner application using <PERSON>wright. The tests simulate real user workflows and verify frontend-backend integration.

## Test Structure

```
tests/
├── auth/                    # Authentication flow tests
├── projects/               # Project management tests
├── tasks/                  # Task management tests
├── team/                   # Team management tests
├── dashboard/              # Dashboard and navigation tests
├── integration/            # User journeys and API integration tests
├── utils/                  # Test utilities and helpers
└── README.md              # This file
```

## Test Categories

### 1. Authentication Tests (`auth/`)
- User registration and validation
- Login/logout functionality
- Authentication state management
- Token handling and expiration
- Navigation guards

### 2. Project Management Tests (`projects/`)
- Project creation, editing, and deletion
- Project status updates
- Team assignment to projects
- Project filtering and searching
- Project validation

### 3. Task Management Tests (`tasks/`)
- Task creation and assignment
- Status updates and progress tracking
- Task filtering by status, priority, assignee
- Task comments and collaboration
- Task workflow management

### 4. Team Management Tests (`team/`)
- Team member management
- Role assignments
- Headcount requests
- Team capacity planning

### 5. Dashboard and Navigation Tests (`dashboard/`)
- Dashboard data display
- Navigation between pages
- Responsive layout verification
- Performance testing
- Loading states

### 6. Integration Tests (`integration/`)
- Complete user journeys (CEO workflows)
- Frontend-backend API integration
- Real-time data synchronization
- Error handling and recovery
- Performance and scalability

## Test Utilities

### TestHelpers Class
Located in `utils/test-helpers.js`, provides common functionality:

- `login()` - Authenticate with test credentials
- `register()` - Create new user accounts
- `createProject()` - Create test projects
- `createTask()` - Create test tasks
- `navigateTo()` - Navigate to specific pages
- `waitForLoading()` - Wait for page loading to complete
- `checkResponsiveLayout()` - Test responsive design
- `takeScreenshot()` - Capture screenshots for debugging

### ApiHelpers Class
Provides direct API testing capabilities:

- `login()` - API authentication
- `authenticatedRequest()` - Make authenticated API calls
- `createTestProject()` - Create projects via API
- `cleanupTestData()` - Clean up test data

## Running Tests

### Prerequisites
1. Start the Django backend server:
   ```bash
   cd .. && uv run python manage.py runserver
   ```

2. Ensure test data is available:
   ```bash
   cd .. && uv run python setup_initial_data.py
   ```

### Test Commands

```bash
# Run all tests
npm run test

# Run tests in headed mode (see browser)
npm run test:headed

# Run tests with UI mode (interactive)
npm run test:ui

# Run specific test file
npm run test tests/auth/authentication.spec.js

# Run tests in debug mode
npm run test:debug

# Generate and view test report
npm run test:report
```

### Test Configuration

The tests are configured in `playwright.config.js`:

- **Browsers**: Chromium, Firefox, WebKit
- **Mobile Testing**: Pixel 5, iPhone 12
- **Base URL**: http://localhost:5173 (Vite dev server)
- **Timeouts**: Configured for reliable testing
- **Screenshots**: Captured on failure
- **Videos**: Recorded on failure
- **Traces**: Collected on retry

## Test Data Requirements

### Test User Account
- Email: `<EMAIL>`
- Password: `testpass123`

### Required Test Data
The tests expect certain initial data to be present:
- AI roles (Product Manager, Frontend Dev, Backend Dev, UI/UX Designer)
- Sample team members
- Sample project data

Run the setup script to ensure test data is available:
```bash
cd .. && uv run python setup_initial_data.py
```

## Test ID Conventions

All interactive elements should have `data-testid` attributes for reliable testing:

```html
<!-- Buttons -->
<button data-testid="login-button">Login</button>
<button data-testid="create-project-button">Create Project</button>

<!-- Forms -->
<input data-testid="email-input" />
<input data-testid="password-input" />

<!-- Navigation -->
<nav data-testid="main-navigation">
<div data-testid="user-menu">

<!-- Content Areas -->
<main data-testid="main-content">
<div data-testid="dashboard-stats">

<!-- Lists and Items -->
<div data-testid="projects-list">
<div data-testid="project-{title}">
```

## Responsive Testing

The tests include comprehensive responsive layout verification:

- **Mobile**: 375x667 (iPhone SE)
- **Tablet**: 768x1024 (iPad)
- **Desktop**: 1920x1080 (Full HD)

Tests verify:
- Navigation accessibility on all screen sizes
- Content visibility and layout
- Mobile menu functionality
- Touch-friendly interactions

## Critical User Journeys

### CEO Complete Workflow
1. Login as CEO
2. Review dashboard metrics
3. Create strategic project
4. Assign team members
5. Create project tasks
6. Monitor progress
7. Handle headcount requests
8. Review final metrics

### Team Collaboration Workflow
1. Task assignment
2. Progress updates
3. Status changes
4. Comment collaboration
5. Task completion

## Error Handling Tests

- Network disconnection scenarios
- API error responses
- Session expiration
- Malformed data handling
- Timeout scenarios

## Performance Testing

- Page load times
- Large dataset handling
- Concurrent request handling
- Memory usage monitoring
- Network request optimization

## Debugging Tests

### Screenshots
Screenshots are automatically captured on test failures and saved to `test-results/`.

### Videos
Test execution videos are recorded for failed tests.

### Traces
Playwright traces are collected on retry, providing detailed execution information.

### Manual Debugging
```bash
# Run specific test in debug mode
npx playwright test tests/auth/authentication.spec.js --debug

# Run with browser visible
npx playwright test --headed

# Run with slow motion
npx playwright test --headed --slowMo=1000
```

## Continuous Integration

The tests are designed to run in CI environments:

- Headless mode by default
- Retry logic for flaky tests
- Parallel execution support
- Comprehensive reporting
- Artifact collection

## Best Practices

1. **Test Independence**: Each test should be independent and not rely on other tests
2. **Data Cleanup**: Use fresh data for each test run
3. **Explicit Waits**: Use `waitForLoading()` instead of fixed timeouts
4. **Page Object Pattern**: Use helper classes for common operations
5. **Descriptive Names**: Test names should clearly describe what is being tested
6. **Error Messages**: Provide clear error messages for test failures

## Troubleshooting

### Common Issues

1. **Backend not running**: Ensure Django server is started on port 8000
2. **Frontend not accessible**: Ensure Vite dev server is running on port 5173
3. **Test data missing**: Run `setup_initial_data.py` to create required data
4. **Authentication failures**: Verify test user credentials are correct
5. **Timeout errors**: Increase timeout values in playwright.config.js

### Debug Commands

```bash
# Check if servers are running
curl http://localhost:8000/api/auth/login/
curl http://localhost:5173

# Verify test data
cd .. && uv run python manage.py shell
>>> from django.contrib.auth import get_user_model
>>> User = get_user_model()
>>> User.objects.filter(email='<EMAIL>').exists()
```

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Add appropriate test IDs to Vue components
3. Use the TestHelpers utilities
4. Include both positive and negative test cases
5. Test responsive behavior
6. Add error handling scenarios
7. Update this README if needed
