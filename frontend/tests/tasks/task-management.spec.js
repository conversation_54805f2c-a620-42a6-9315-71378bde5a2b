import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers.js';

test.describe('Task Management', () => {
  let helpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.login();
  });

  test.describe('Task Creation', () => {
    test('should create a new task successfully', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      // Verify task appears in the list
      await helpers.navigateTo('/tasks');
      await expect(page.locator(`[data-testid="task-${taskData.title}"]`)).toBeVisible();
      
      // Verify task details
      await page.click(`[data-testid="task-${taskData.title}"]`);
      await expect(page.locator('[data-testid="task-title"]')).toContainText(taskData.title);
      await expect(page.locator('[data-testid="task-description"]')).toContainText(taskData.description);
    });

    test('should validate required fields when creating task', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      await page.click('[data-testid="create-task-button"]');
      
      // Try to save without filling required fields
      await page.click('[data-testid="save-task-button"]');
      
      // Check for validation errors
      await expect(page.locator('[data-testid="title-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="description-error"]')).toBeVisible();
    });

    test('should create task with different priorities', async ({ page }) => {
      const priorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT'];
      
      for (const priority of priorities) {
        const taskData = await helpers.createTask({ priority });
        
        // Verify task was created with correct priority
        await helpers.navigateTo('/tasks');
        const taskCard = page.locator(`[data-testid="task-${taskData.title}"]`);
        await expect(taskCard.locator('[data-testid="task-priority"]')).toContainText(priority);
      }
    });

    test('should assign task to team member during creation', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      await page.click('[data-testid="create-task-button"]');
      
      // Fill task details
      await page.fill('[data-testid="task-title-input"]', 'Assigned Task Test');
      await page.fill('[data-testid="task-description-input"]', 'Task with assignee');
      await page.selectOption('[data-testid="task-assignee-select"]', { index: 1 }); // Select first available member
      
      await page.click('[data-testid="save-task-button"]');
      
      // Verify task was assigned
      await expect(page.locator('[data-testid="task-Assigned Task Test"] [data-testid="task-assignee"]')).toBeVisible();
    });
  });

  test.describe('Task Listing and Filtering', () => {
    test('should display all tasks in the list', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      
      // Wait for tasks to load
      await helpers.waitForLoading();
      
      // Check if tasks list is visible
      await expect(page.locator('[data-testid="tasks-list"]')).toBeVisible();
      
      // Verify at least one task exists
      await expect(page.locator('[data-testid^="task-"]')).toHaveCountGreaterThan(0);
    });

    test('should filter tasks by status', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      
      // Test different status filters
      const statuses = ['TODO', 'IN_PROGRESS', 'REVIEW', 'DONE'];
      
      for (const status of statuses) {
        await page.selectOption('[data-testid="status-filter"]', status);
        await helpers.waitForLoading();
        
        // Verify filtered results
        const taskCards = page.locator('[data-testid^="task-"]');
        const count = await taskCards.count();
        
        if (count > 0) {
          // Check that all visible tasks have the selected status
          for (let i = 0; i < count; i++) {
            await expect(taskCards.nth(i).locator('[data-testid="task-status"]')).toContainText(status);
          }
        }
      }
    });

    test('should filter tasks by priority', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      
      // Test priority filters
      const priorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT'];
      
      for (const priority of priorities) {
        await page.selectOption('[data-testid="priority-filter"]', priority);
        await helpers.waitForLoading();
        
        // Verify filtered results
        const taskCards = page.locator('[data-testid^="task-"]');
        const count = await taskCards.count();
        
        if (count > 0) {
          for (let i = 0; i < count; i++) {
            await expect(taskCards.nth(i).locator('[data-testid="task-priority"]')).toContainText(priority);
          }
        }
      }
    });

    test('should filter tasks by assignee', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      
      // Filter by assignee
      await page.selectOption('[data-testid="assignee-filter"]', { index: 1 }); // Select first assignee
      await helpers.waitForLoading();
      
      // Verify filtered results show only tasks for selected assignee
      const taskCards = page.locator('[data-testid^="task-"]');
      const count = await taskCards.count();
      
      if (count > 0) {
        // All visible tasks should have the selected assignee
        for (let i = 0; i < count; i++) {
          await expect(taskCards.nth(i).locator('[data-testid="task-assignee"]')).toBeVisible();
        }
      }
    });

    test('should search tasks by title', async ({ page }) => {
      // Create a unique task for searching
      const taskData = await helpers.createTask({ title: 'Unique Search Task Test' });
      
      await helpers.navigateTo('/tasks');
      
      // Search for the task
      await page.fill('[data-testid="search-input"]', 'Unique Search');
      await helpers.waitForLoading();
      
      // Verify search results
      await expect(page.locator(`[data-testid="task-${taskData.title}"]`)).toBeVisible();
      
      // Clear search and verify all tasks are shown again
      await page.fill('[data-testid="search-input"]', '');
      await helpers.waitForLoading();
      await expect(page.locator('[data-testid^="task-"]')).toHaveCountGreaterThan(1);
    });
  });

  test.describe('Task Status Updates', () => {
    test('should update task status', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      // Navigate to tasks and update status
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Update status to IN_PROGRESS
      await page.selectOption('[data-testid="task-status-select"]', 'IN_PROGRESS');
      await page.click('[data-testid="update-status-button"]');
      
      // Verify status was updated
      await expect(page.locator('[data-testid="task-status"]')).toContainText('IN_PROGRESS');
      
      // Verify status is updated in the tasks list
      await helpers.navigateTo('/tasks');
      await expect(page.locator(`[data-testid="task-${taskData.title}"] [data-testid="task-status"]`)).toContainText('IN_PROGRESS');
    });

    test('should track task progress through workflow', async ({ page }) => {
      const taskData = await helpers.createTask();
      const statuses = ['TODO', 'IN_PROGRESS', 'REVIEW', 'DONE'];
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Move task through each status
      for (const status of statuses) {
        await page.selectOption('[data-testid="task-status-select"]', status);
        await page.click('[data-testid="update-status-button"]');
        
        // Verify status was updated
        await expect(page.locator('[data-testid="task-status"]')).toContainText(status);
        
        // Wait a moment for the update to process
        await page.waitForTimeout(500);
      }
    });

    test('should update task progress percentage', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Update progress
      await page.fill('[data-testid="task-progress-input"]', '75');
      await page.click('[data-testid="update-progress-button"]');
      
      // Verify progress was updated
      await expect(page.locator('[data-testid="task-progress"]')).toContainText('75%');
    });
  });

  test.describe('Task Assignment and Reassignment', () => {
    test('should assign task to team member', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Assign task to a team member
      await page.selectOption('[data-testid="task-assignee-select"]', { index: 1 });
      await page.click('[data-testid="assign-task-button"]');
      
      // Verify assignment
      await expect(page.locator('[data-testid="task-assignee"]')).toBeVisible();
    });

    test('should reassign task to different team member', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Assign to first member
      await page.selectOption('[data-testid="task-assignee-select"]', { index: 1 });
      await page.click('[data-testid="assign-task-button"]');
      
      // Reassign to different member
      await page.selectOption('[data-testid="task-assignee-select"]', { index: 2 });
      await page.click('[data-testid="assign-task-button"]');
      
      // Verify reassignment
      await expect(page.locator('[data-testid="task-assignee"]')).toBeVisible();
    });

    test('should unassign task', async ({ page }) => {
      const taskData = await helpers.createTask({ assignee: 'test-member' });
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Unassign task
      await page.click('[data-testid="unassign-task-button"]');
      await page.click('[data-testid="confirm-unassign-button"]');
      
      // Verify task is unassigned
      await expect(page.locator('[data-testid="task-assignee"]')).toContainText('Unassigned');
    });
  });

  test.describe('Task Editing and Details', () => {
    test('should edit task information', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      await page.click('[data-testid="edit-task-button"]');
      
      // Update task information
      const updatedTitle = `${taskData.title} - Updated`;
      await page.fill('[data-testid="task-title-input"]', updatedTitle);
      await page.fill('[data-testid="task-description-input"]', 'Updated task description');
      await page.selectOption('[data-testid="task-priority-select"]', 'HIGH');
      
      await page.click('[data-testid="save-task-button"]');
      
      // Verify changes were saved
      await expect(page.locator('[data-testid="task-title"]')).toContainText(updatedTitle);
      await expect(page.locator('[data-testid="task-description"]')).toContainText('Updated task description');
      await expect(page.locator('[data-testid="task-priority"]')).toContainText('HIGH');
    });

    test('should add and view task comments', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Add a comment
      await page.fill('[data-testid="comment-input"]', 'This is a test comment');
      await page.click('[data-testid="add-comment-button"]');
      
      // Verify comment was added
      await expect(page.locator('[data-testid="task-comments"]')).toContainText('This is a test comment');
    });

    test('should update task estimated hours', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      await page.click('[data-testid="edit-task-button"]');
      
      // Update estimated hours
      await page.fill('[data-testid="task-hours-input"]', '16');
      await page.click('[data-testid="save-task-button"]');
      
      // Verify hours were updated
      await expect(page.locator('[data-testid="task-estimated-hours"]')).toContainText('16');
    });
  });

  test.describe('Task Deletion', () => {
    test('should delete task with confirmation', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Delete task
      await page.click('[data-testid="delete-task-button"]');
      await page.click('[data-testid="confirm-delete-button"]');
      
      // Verify task was deleted
      await helpers.navigateTo('/tasks');
      await expect(page.locator(`[data-testid="task-${taskData.title}"]`)).not.toBeVisible();
    });

    test('should cancel task deletion', async ({ page }) => {
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Try to delete but cancel
      await page.click('[data-testid="delete-task-button"]');
      await page.click('[data-testid="cancel-delete-button"]');
      
      // Verify task still exists
      await helpers.navigateTo('/tasks');
      await expect(page.locator(`[data-testid="task-${taskData.title}"]`)).toBeVisible();
    });
  });
});
