import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers.js';

test.describe('Responsive Layout Issues Detection', () => {
  let helpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.login();
  });

  test.describe('Task Management Component Responsive Issues', () => {
    test('should identify task list layout issues on mobile', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      await helpers.waitForLoading();
      
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      // Check if task cards are properly stacked on mobile
      const taskCards = page.locator('[data-testid^="task-"]');
      const count = await taskCards.count();
      
      if (count > 0) {
        // Check if task cards overflow horizontally
        const firstCard = taskCards.first();
        const cardBox = await firstCard.boundingBox();
        
        // Task card should not exceed viewport width
        expect(cardBox.width).toBeLessThanOrEqual(375);
        
        // Check if task content is readable on mobile
        const taskTitle = firstCard.locator('[data-testid="task-title"]');
        const titleBox = await taskTitle.boundingBox();
        
        // Title should be visible and not cut off
        expect(titleBox.width).toBeGreaterThan(0);
        expect(titleBox.width).toBeLessThanOrEqual(350); // Allow for padding
        
        // Check if action buttons are accessible on mobile
        const actionButtons = firstCard.locator('button');
        const buttonCount = await actionButtons.count();
        
        if (buttonCount > 0) {
          const firstButton = actionButtons.first();
          const buttonBox = await firstButton.boundingBox();
          
          // Buttons should be large enough for touch interaction
          expect(buttonBox.height).toBeGreaterThanOrEqual(44); // iOS minimum touch target
          expect(buttonBox.width).toBeGreaterThanOrEqual(44);
        }
      }
      
      // Take screenshot for manual review
      await helpers.takeScreenshot('task-list-mobile-layout');
    });

    test('should identify task creation form issues on mobile', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      // Try to open task creation form
      if (await helpers.isVisible('[data-testid="create-task-button"]')) {
        await page.click('[data-testid="create-task-button"]');
        
        // Check if form is properly displayed on mobile
        const form = page.locator('form');
        const formBox = await form.boundingBox();
        
        if (formBox) {
          // Form should not exceed viewport width
          expect(formBox.width).toBeLessThanOrEqual(375);
          
          // Check if form inputs are properly sized
          const inputs = form.locator('input, textarea, select');
          const inputCount = await inputs.count();
          
          for (let i = 0; i < inputCount; i++) {
            const input = inputs.nth(i);
            const inputBox = await input.boundingBox();
            
            if (inputBox) {
              // Inputs should be wide enough but not overflow
              expect(inputBox.width).toBeGreaterThan(200);
              expect(inputBox.width).toBeLessThanOrEqual(350);
              
              // Inputs should be tall enough for touch interaction
              expect(inputBox.height).toBeGreaterThanOrEqual(40);
            }
          }
        }
        
        await helpers.takeScreenshot('task-form-mobile-layout');
      }
    });

    test('should identify task detail view issues on mobile', async ({ page }) => {
      // Create a test task first
      const taskData = await helpers.createTask();
      
      await helpers.navigateTo('/tasks');
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      // Open task details
      if (await helpers.isVisible(`[data-testid="task-${taskData.title}"]`)) {
        await page.click(`[data-testid="task-${taskData.title}"]`);
        
        // Check task detail layout
        const detailContainer = page.locator('[data-testid="task-details"]');
        
        if (await detailContainer.isVisible()) {
          const containerBox = await detailContainer.boundingBox();
          
          // Detail view should fit in mobile viewport
          expect(containerBox.width).toBeLessThanOrEqual(375);
          
          // Check if task information is properly displayed
          const taskInfo = detailContainer.locator('[data-testid="task-info"]');
          if (await taskInfo.isVisible()) {
            const infoBox = await taskInfo.boundingBox();
            expect(infoBox.width).toBeLessThanOrEqual(350);
          }
          
          // Check if action buttons are accessible
          const actionSection = detailContainer.locator('[data-testid="task-actions"]');
          if (await actionSection.isVisible()) {
            const buttons = actionSection.locator('button');
            const buttonCount = await buttons.count();
            
            for (let i = 0; i < buttonCount; i++) {
              const button = buttons.nth(i);
              const buttonBox = await button.boundingBox();
              
              if (buttonBox) {
                // Buttons should be touch-friendly
                expect(buttonBox.height).toBeGreaterThanOrEqual(44);
                expect(buttonBox.width).toBeGreaterThanOrEqual(44);
              }
            }
          }
        }
        
        await helpers.takeScreenshot('task-detail-mobile-layout');
      }
    });
  });

  test.describe('TODO Component Responsive Issues', () => {
    test('should identify TODO list layout issues across breakpoints', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      await helpers.waitForLoading();
      
      const breakpoints = [
        { name: 'mobile', width: 375, height: 667 },
        { name: 'tablet', width: 768, height: 1024 },
        { name: 'desktop', width: 1200, height: 800 },
        { name: 'large-desktop', width: 1920, height: 1080 }
      ];
      
      for (const breakpoint of breakpoints) {
        await page.setViewportSize({ width: breakpoint.width, height: breakpoint.height });
        await page.waitForTimeout(500);
        
        // Check if TODO/task list adapts properly
        const tasksList = page.locator('[data-testid="tasks-list"]');
        
        if (await tasksList.isVisible()) {
          const listBox = await tasksList.boundingBox();
          
          // List should not exceed viewport width
          expect(listBox.width).toBeLessThanOrEqual(breakpoint.width);
          
          // Check task card layout
          const taskCards = tasksList.locator('[data-testid^="task-"]');
          const cardCount = await taskCards.count();
          
          if (cardCount > 0) {
            const firstCard = taskCards.first();
            const cardBox = await firstCard.boundingBox();
            
            // Cards should be appropriately sized for breakpoint
            if (breakpoint.width < 768) {
              // Mobile: cards should stack vertically and use full width
              expect(cardBox.width).toBeGreaterThan(breakpoint.width * 0.8);
            } else if (breakpoint.width < 1200) {
              // Tablet: cards can be in 2 columns
              expect(cardBox.width).toBeLessThanOrEqual(breakpoint.width * 0.6);
            } else {
              // Desktop: cards can be in multiple columns
              expect(cardBox.width).toBeLessThanOrEqual(breakpoint.width * 0.4);
            }
          }
        }
        
        // Check filter controls layout
        const filters = page.locator('[data-testid="task-filters"]');
        if (await filters.isVisible()) {
          const filtersBox = await filters.boundingBox();
          
          // Filters should not overflow
          expect(filtersBox.width).toBeLessThanOrEqual(breakpoint.width);
          
          // On mobile, filters might stack vertically
          if (breakpoint.width < 768) {
            const filterInputs = filters.locator('input, select');
            const inputCount = await filterInputs.count();
            
            if (inputCount > 1) {
              // Check if filters are properly stacked on mobile
              const firstInput = filterInputs.first();
              const secondInput = filterInputs.nth(1);
              
              const firstBox = await firstInput.boundingBox();
              const secondBox = await secondInput.boundingBox();
              
              if (firstBox && secondBox) {
                // Second input should be below first (vertical stacking)
                expect(secondBox.y).toBeGreaterThan(firstBox.y + firstBox.height - 10);
              }
            }
          }
        }
        
        await helpers.takeScreenshot(`todo-layout-${breakpoint.name}`);
      }
    });

    test('should identify TODO item interaction issues on touch devices', async ({ page }) => {
      await helpers.navigateTo('/tasks');
      await helpers.waitForLoading();
      
      // Simulate touch device
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      const taskCards = page.locator('[data-testid^="task-"]');
      const cardCount = await taskCards.count();
      
      if (cardCount > 0) {
        const firstCard = taskCards.first();
        
        // Check if task card has proper touch targets
        const interactiveElements = firstCard.locator('button, a, [role="button"]');
        const elementCount = await interactiveElements.count();
        
        for (let i = 0; i < elementCount; i++) {
          const element = interactiveElements.nth(i);
          const elementBox = await element.boundingBox();
          
          if (elementBox) {
            // Touch targets should be at least 44x44px (iOS guidelines)
            const isTouchFriendly = elementBox.width >= 44 && elementBox.height >= 44;
            
            if (!isTouchFriendly) {
              console.log(`Touch target too small: ${elementBox.width}x${elementBox.height}`);
              
              // Take screenshot of problematic element
              await element.screenshot({ 
                path: `test-results/small-touch-target-${i}.png` 
              });
            }
            
            expect(isTouchFriendly).toBeTruthy();
          }
        }
        
        // Check spacing between interactive elements
        if (elementCount > 1) {
          for (let i = 0; i < elementCount - 1; i++) {
            const currentElement = interactiveElements.nth(i);
            const nextElement = interactiveElements.nth(i + 1);
            
            const currentBox = await currentElement.boundingBox();
            const nextBox = await nextElement.boundingBox();
            
            if (currentBox && nextBox) {
              // Calculate distance between elements
              const horizontalDistance = Math.abs(nextBox.x - (currentBox.x + currentBox.width));
              const verticalDistance = Math.abs(nextBox.y - (currentBox.y + currentBox.height));
              
              // Elements should have adequate spacing (at least 8px)
              const hasAdequateSpacing = horizontalDistance >= 8 || verticalDistance >= 8;
              
              if (!hasAdequateSpacing) {
                console.log(`Insufficient spacing between touch targets: ${horizontalDistance}px horizontal, ${verticalDistance}px vertical`);
              }
              
              expect(hasAdequateSpacing).toBeTruthy();
            }
          }
        }
      }
      
      await helpers.takeScreenshot('todo-touch-interactions');
    });
  });

  test.describe('Layout Issue Reporting', () => {
    test('should generate comprehensive layout report', async ({ page }) => {
      const layoutIssues = [];
      
      const pages = [
        { path: '/', name: 'Dashboard' },
        { path: '/projects', name: 'Projects' },
        { path: '/tasks', name: 'Tasks' },
        { path: '/team', name: 'Team' }
      ];
      
      const breakpoints = [
        { name: 'mobile', width: 375, height: 667 },
        { name: 'tablet', width: 768, height: 1024 },
        { name: 'desktop', width: 1920, height: 1080 }
      ];
      
      for (const pageInfo of pages) {
        await helpers.navigateTo(pageInfo.path);
        await helpers.waitForLoading();
        
        for (const breakpoint of breakpoints) {
          await page.setViewportSize({ width: breakpoint.width, height: breakpoint.height });
          await page.waitForTimeout(500);
          
          // Check for horizontal scrollbars (usually indicates layout issues)
          const hasHorizontalScroll = await page.evaluate(() => {
            return document.documentElement.scrollWidth > document.documentElement.clientWidth;
          });
          
          if (hasHorizontalScroll) {
            layoutIssues.push({
              page: pageInfo.name,
              breakpoint: breakpoint.name,
              issue: 'Horizontal scrollbar detected',
              viewport: `${breakpoint.width}x${breakpoint.height}`
            });
          }
          
          // Check for elements extending beyond viewport
          const overflowingElements = await page.evaluate((viewportWidth) => {
            const elements = document.querySelectorAll('*');
            const overflowing = [];
            
            elements.forEach((el, index) => {
              const rect = el.getBoundingClientRect();
              if (rect.right > viewportWidth) {
                overflowing.push({
                  tagName: el.tagName,
                  className: el.className,
                  id: el.id,
                  width: rect.width,
                  right: rect.right,
                  index
                });
              }
            });
            
            return overflowing;
          }, breakpoint.width);
          
          if (overflowingElements.length > 0) {
            layoutIssues.push({
              page: pageInfo.name,
              breakpoint: breakpoint.name,
              issue: 'Elements extending beyond viewport',
              details: overflowingElements.slice(0, 5), // Limit to first 5
              viewport: `${breakpoint.width}x${breakpoint.height}`
            });
          }
          
          await helpers.takeScreenshot(`layout-check-${pageInfo.name}-${breakpoint.name}`);
        }
      }
      
      // Log layout issues for review
      if (layoutIssues.length > 0) {
        console.log('Layout Issues Detected:');
        console.log(JSON.stringify(layoutIssues, null, 2));
        
        // Save layout issues to file for CI/CD integration
        await page.evaluate((issues) => {
          const blob = new Blob([JSON.stringify(issues, null, 2)], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'layout-issues-report.json';
          a.click();
        }, layoutIssues);
      }
      
      // Test should pass but report issues for manual review
      expect(layoutIssues.length).toBeLessThan(50); // Reasonable threshold
    });
  });
});
