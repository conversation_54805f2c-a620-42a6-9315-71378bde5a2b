import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers.js';

test.describe('Project Management', () => {
  let helpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.login();
  });

  test.describe('Project Creation', () => {
    test('should create a new project successfully', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Verify project appears in the list
      await helpers.navigateTo('/projects');
      await expect(page.locator(`[data-testid="project-${projectData.title}"]`)).toBeVisible();
      
      // Verify project details
      await page.click(`[data-testid="project-${projectData.title}"]`);
      await expect(page.locator('[data-testid="project-title"]')).toContainText(projectData.title);
      await expect(page.locator('[data-testid="project-description"]')).toContainText(projectData.description);
    });

    test('should validate required fields when creating project', async ({ page }) => {
      await helpers.navigateTo('/projects');
      await page.click('[data-testid="create-project-button"]');
      
      // Try to save without filling required fields
      await page.click('[data-testid="save-project-button"]');
      
      // Check for validation errors
      await expect(page.locator('[data-testid="title-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="description-error"]')).toBeVisible();
    });

    test('should handle different project priorities', async ({ page }) => {
      const priorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT'];
      
      for (const priority of priorities) {
        const projectData = await helpers.createProject({ priority });
        
        // Verify project was created with correct priority
        await helpers.navigateTo('/projects');
        const projectCard = page.locator(`[data-testid="project-${projectData.title}"]`);
        await expect(projectCard.locator('[data-testid="project-priority"]')).toContainText(priority);
      }
    });
  });

  test.describe('Project Listing and Filtering', () => {
    test('should display all projects in the list', async ({ page }) => {
      await helpers.navigateTo('/projects');
      
      // Wait for projects to load
      await helpers.waitForLoading();
      
      // Check if projects list is visible
      await expect(page.locator('[data-testid="projects-list"]')).toBeVisible();
      
      // Verify at least one project exists (from test data)
      await expect(page.locator('[data-testid^="project-"]')).toHaveCountGreaterThan(0);
    });

    test('should filter projects by status', async ({ page }) => {
      await helpers.navigateTo('/projects');
      
      // Test different status filters
      const statuses = ['PLANNING', 'IN_PROGRESS', 'COMPLETED'];
      
      for (const status of statuses) {
        await page.selectOption('[data-testid="status-filter"]', status);
        await helpers.waitForLoading();
        
        // Verify filtered results
        const projectCards = page.locator('[data-testid^="project-"]');
        const count = await projectCards.count();
        
        if (count > 0) {
          // Check that all visible projects have the selected status
          for (let i = 0; i < count; i++) {
            await expect(projectCards.nth(i).locator('[data-testid="project-status"]')).toContainText(status);
          }
        }
      }
    });

    test('should search projects by title', async ({ page }) => {
      await helpers.navigateTo('/projects');
      
      // Create a test project first
      const projectData = await helpers.createProject({ title: 'Unique Search Test Project' });
      
      // Search for the project
      await page.fill('[data-testid="search-input"]', 'Unique Search');
      await helpers.waitForLoading();
      
      // Verify search results
      await expect(page.locator(`[data-testid="project-${projectData.title}"]`)).toBeVisible();
      
      // Clear search and verify all projects are shown again
      await page.fill('[data-testid="search-input"]', '');
      await helpers.waitForLoading();
      await expect(page.locator('[data-testid^="project-"]')).toHaveCountGreaterThan(1);
    });
  });

  test.describe('Project Details and Editing', () => {
    test('should view project details', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Navigate to project details
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      
      // Verify all project details are displayed
      await expect(page.locator('[data-testid="project-title"]')).toContainText(projectData.title);
      await expect(page.locator('[data-testid="project-description"]')).toContainText(projectData.description);
      await expect(page.locator('[data-testid="project-requirements"]')).toContainText(projectData.requirements);
      await expect(page.locator('[data-testid="project-priority"]')).toContainText(projectData.priority);
      await expect(page.locator('[data-testid="project-budget"]')).toContainText(projectData.budget);
    });

    test('should edit project information', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Navigate to project details and edit
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      await page.click('[data-testid="edit-project-button"]');
      
      // Update project information
      const updatedTitle = `${projectData.title} - Updated`;
      await page.fill('[data-testid="project-title-input"]', updatedTitle);
      await page.fill('[data-testid="project-description-input"]', 'Updated description');
      await page.selectOption('[data-testid="project-priority-select"]', 'URGENT');
      
      await page.click('[data-testid="save-project-button"]');
      
      // Verify changes were saved
      await expect(page.locator('[data-testid="project-title"]')).toContainText(updatedTitle);
      await expect(page.locator('[data-testid="project-description"]')).toContainText('Updated description');
      await expect(page.locator('[data-testid="project-priority"]')).toContainText('URGENT');
    });

    test('should update project status', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Navigate to project details
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      
      // Update project status
      await page.selectOption('[data-testid="project-status-select"]', 'IN_PROGRESS');
      await page.click('[data-testid="update-status-button"]');
      
      // Verify status was updated
      await expect(page.locator('[data-testid="project-status"]')).toContainText('IN_PROGRESS');
      
      // Verify status is updated in the projects list
      await helpers.navigateTo('/projects');
      await expect(page.locator(`[data-testid="project-${projectData.title}"] [data-testid="project-status"]`)).toContainText('IN_PROGRESS');
    });
  });

  test.describe('Project Team Assignment', () => {
    test('should assign team members to project', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Navigate to project details
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      
      // Go to team assignment section
      await page.click('[data-testid="team-tab"]');
      
      // Assign team members
      await page.click('[data-testid="assign-member-button"]');
      await page.selectOption('[data-testid="member-select"]', { index: 1 }); // Select first available member
      await page.selectOption('[data-testid="role-select"]', 'Developer');
      await page.click('[data-testid="confirm-assignment-button"]');
      
      // Verify team member was assigned
      await expect(page.locator('[data-testid="assigned-members"]')).toContainText('Developer');
    });

    test('should remove team members from project', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Navigate to project and assign a member first
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      await page.click('[data-testid="team-tab"]');
      
      // Assign a member
      await page.click('[data-testid="assign-member-button"]');
      await page.selectOption('[data-testid="member-select"]', { index: 1 });
      await page.selectOption('[data-testid="role-select"]', 'Developer');
      await page.click('[data-testid="confirm-assignment-button"]');
      
      // Remove the member
      await page.click('[data-testid="remove-member-button"]');
      await page.click('[data-testid="confirm-removal-button"]');
      
      // Verify member was removed
      await expect(page.locator('[data-testid="assigned-members"]')).not.toContainText('Developer');
    });
  });

  test.describe('Project Deletion', () => {
    test('should delete project with confirmation', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Navigate to project details
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      
      // Delete project
      await page.click('[data-testid="delete-project-button"]');
      await page.click('[data-testid="confirm-delete-button"]');
      
      // Verify project was deleted
      await helpers.navigateTo('/projects');
      await expect(page.locator(`[data-testid="project-${projectData.title}"]`)).not.toBeVisible();
    });

    test('should cancel project deletion', async ({ page }) => {
      const projectData = await helpers.createProject();
      
      // Navigate to project details
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      
      // Try to delete but cancel
      await page.click('[data-testid="delete-project-button"]');
      await page.click('[data-testid="cancel-delete-button"]');
      
      // Verify project still exists
      await helpers.navigateTo('/projects');
      await expect(page.locator(`[data-testid="project-${projectData.title}"]`)).toBeVisible();
    });
  });
});
