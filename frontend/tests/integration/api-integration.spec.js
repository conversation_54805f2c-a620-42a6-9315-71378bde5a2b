import { test, expect } from '@playwright/test';
import { TestHelpers, ApiHelpers } from '../utils/test-helpers.js';

test.describe('Frontend-Backend Integration', () => {
  let helpers;
  let apiHelpers;

  test.beforeEach(async ({ page, request }) => {
    helpers = new TestHelpers(page);
    apiHelpers = new ApiHelpers(request);
  });

  test.describe('API Proxy Configuration', () => {
    test('should proxy API requests correctly through Vite', async ({ page }) => {
      await helpers.login();
      
      // Monitor network requests
      const apiRequests = [];
      page.on('request', request => {
        if (request.url().includes('/api/')) {
          apiRequests.push({
            url: request.url(),
            method: request.method(),
            headers: request.headers()
          });
        }
      });
      
      // Navigate to dashboard to trigger API calls
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Verify API requests are being made through proxy
      expect(apiRequests.length).toBeGreaterThan(0);
      
      // Check that requests are going to the correct backend
      const projectsRequest = apiRequests.find(req => req.url.includes('/api/projects/'));
      expect(projectsRequest).toBeTruthy();
      expect(projectsRequest.url).toContain('localhost:5173'); // Frontend URL with proxy
      
      // Verify authentication headers are included
      expect(projectsRequest.headers.authorization).toBeTruthy();
    });

    test('should handle CORS correctly', async ({ page }) => {
      await helpers.login();
      
      // Monitor for CORS errors
      const consoleErrors = [];
      page.on('console', msg => {
        if (msg.type() === 'error' && msg.text().includes('CORS')) {
          consoleErrors.push(msg.text());
        }
      });
      
      // Perform various API operations
      await helpers.navigateTo('/projects');
      await helpers.waitForLoading();
      
      await helpers.navigateTo('/tasks');
      await helpers.waitForLoading();
      
      await helpers.navigateTo('/team');
      await helpers.waitForLoading();
      
      // Verify no CORS errors occurred
      expect(consoleErrors).toHaveLength(0);
    });

    test('should handle API timeouts gracefully', async ({ page }) => {
      await helpers.login();
      
      // Simulate slow API response
      await page.route('**/api/projects/', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
        route.continue();
      });
      
      await helpers.navigateTo('/projects');
      
      // Verify loading state is shown
      await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
      
      // Wait for request to complete
      await helpers.waitForLoading();
      
      // Verify content loads after delay
      await expect(page.locator('[data-testid="projects-list"]')).toBeVisible();
    });
  });

  test.describe('Authentication Integration', () => {
    test('should handle authentication flow end-to-end', async ({ page, request }) => {
      // Test login via API
      const loginResponse = await apiHelpers.login();
      expect(loginResponse.user).toBeTruthy();
      expect(loginResponse.token).toBeTruthy();
      
      // Test login via UI
      await helpers.login();
      
      // Verify token is stored and used
      const token = await page.evaluate(() => localStorage.getItem('token'));
      expect(token).toBeTruthy();
      
      // Verify authenticated requests work
      await helpers.navigateTo('/projects');
      await helpers.waitForApiResponse('/api/projects/');
      
      // Verify logout clears authentication
      await helpers.logout();
      const tokenAfterLogout = await page.evaluate(() => localStorage.getItem('token'));
      expect(tokenAfterLogout).toBeFalsy();
    });

    test('should refresh token automatically', async ({ page }) => {
      await helpers.login();
      
      // Simulate token near expiration
      await page.evaluate(() => {
        // Set a token that will expire soon
        const expiredToken = 'expired_token_simulation';
        localStorage.setItem('token', expiredToken);
      });
      
      // Make an API request that should trigger token refresh
      await helpers.navigateTo('/projects');
      
      // Should either refresh token or redirect to login
      await page.waitForTimeout(2000);
      
      const currentUrl = page.url();
      const isLoggedIn = currentUrl.includes('/projects') || currentUrl === '/';
      const isLoginPage = currentUrl.includes('/login');
      
      expect(isLoggedIn || isLoginPage).toBeTruthy();
    });

    test('should handle concurrent API requests with authentication', async ({ page }) => {
      await helpers.login();
      
      // Monitor all API requests
      const apiRequests = [];
      page.on('response', response => {
        if (response.url().includes('/api/')) {
          apiRequests.push({
            url: response.url(),
            status: response.status(),
            headers: response.headers()
          });
        }
      });
      
      // Navigate to dashboard which makes multiple concurrent API calls
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Verify all requests succeeded
      const failedRequests = apiRequests.filter(req => req.status >= 400);
      expect(failedRequests).toHaveLength(0);
      
      // Verify authentication was included in all requests
      const authenticatedRequests = apiRequests.filter(req => 
        req.url.includes('/api/') && !req.url.includes('/auth/login')
      );
      expect(authenticatedRequests.length).toBeGreaterThan(0);
    });
  });

  test.describe('Data Synchronization', () => {
    test('should sync data between frontend and backend', async ({ page, request }) => {
      await helpers.login();
      await apiHelpers.login();
      
      // Create project via API
      const apiProject = await apiHelpers.createTestProject({
        title: 'API Created Project',
        description: 'Project created via API for sync testing'
      });
      
      expect(apiProject.ok()).toBeTruthy();
      const projectData = await apiProject.json();
      
      // Verify project appears in frontend
      await helpers.navigateTo('/projects');
      await helpers.waitForLoading();
      
      await expect(page.locator(`[data-testid="project-${projectData.title}"]`)).toBeVisible();
      
      // Update project via frontend
      await page.click(`[data-testid="project-${projectData.title}"]`);
      await page.click('[data-testid="edit-project-button"]');
      await page.fill('[data-testid="project-description-input"]', 'Updated via frontend');
      await page.click('[data-testid="save-project-button"]');
      
      // Verify update via API
      const updatedProject = await apiHelpers.authenticatedRequest('GET', `/projects/${projectData.id}/`);
      expect(updatedProject.ok()).toBeTruthy();
      const updatedData = await updatedProject.json();
      expect(updatedData.description).toContain('Updated via frontend');
    });

    test('should handle real-time updates', async ({ page }) => {
      await helpers.login();
      
      // Navigate to dashboard
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Get initial project count
      const initialCount = await page.locator('[data-testid="projects-count"]').textContent();
      
      // Create project in another tab/window (simulating another user)
      const newProject = await helpers.createProject({
        title: 'Real-time Test Project'
      });
      
      // Refresh dashboard to see updates
      await page.click('[data-testid="refresh-dashboard-button"]');
      await helpers.waitForLoading();
      
      // Verify count increased
      const newCount = await page.locator('[data-testid="projects-count"]').textContent();
      expect(parseInt(newCount)).toBeGreaterThan(parseInt(initialCount));
    });

    test('should handle optimistic updates', async ({ page }) => {
      await helpers.login();
      
      // Create a task
      const taskData = await helpers.createTask();
      
      // Navigate to task details
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      
      // Update task status - should show immediately (optimistic update)
      await page.selectOption('[data-testid="task-status-select"]', 'IN_PROGRESS');
      await page.click('[data-testid="update-status-button"]');
      
      // Status should update immediately in UI
      await expect(page.locator('[data-testid="task-status"]')).toContainText('IN_PROGRESS');
      
      // Verify the update persists after page refresh
      await page.reload();
      await helpers.waitForLoading();
      await expect(page.locator('[data-testid="task-status"]')).toContainText('IN_PROGRESS');
    });
  });

  test.describe('Error Handling and Recovery', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      await helpers.login();
      
      // Simulate API error
      await page.route('**/api/projects/', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });
      
      await helpers.navigateTo('/projects');
      
      // Verify error is handled gracefully
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText('error');
      
      // Verify retry mechanism works
      await page.unroute('**/api/projects/');
      await page.click('[data-testid="retry-button"]');
      
      await helpers.waitForLoading();
      await expect(page.locator('[data-testid="projects-list"]')).toBeVisible();
    });

    test('should handle network disconnection', async ({ page }) => {
      await helpers.login();
      
      // Navigate to a page
      await helpers.navigateTo('/projects');
      await helpers.waitForLoading();
      
      // Simulate network disconnection
      await page.route('**/*', route => route.abort());
      
      // Try to perform an action
      await page.click('[data-testid="create-project-button"]');
      
      // Should show network error
      await expect(page.locator('[data-testid="network-error"]')).toBeVisible();
      
      // Restore network
      await page.unroute('**/*');
      
      // Should be able to continue
      await page.click('[data-testid="retry-button"]');
      await helpers.waitForLoading();
    });

    test('should handle malformed API responses', async ({ page }) => {
      await helpers.login();
      
      // Simulate malformed JSON response
      await page.route('**/api/projects/', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: 'invalid json response'
        });
      });
      
      await helpers.navigateTo('/projects');
      
      // Should handle parsing error gracefully
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    });
  });

  test.describe('Performance and Caching', () => {
    test('should cache API responses appropriately', async ({ page }) => {
      await helpers.login();
      
      // Monitor network requests
      const requests = [];
      page.on('request', request => {
        if (request.url().includes('/api/projects/')) {
          requests.push(request);
        }
      });
      
      // Navigate to projects page
      await helpers.navigateTo('/projects');
      await helpers.waitForLoading();
      
      const initialRequestCount = requests.length;
      
      // Navigate away and back
      await helpers.navigateTo('/tasks');
      await helpers.navigateTo('/projects');
      await helpers.waitForLoading();
      
      // Should use cached data or make minimal requests
      const finalRequestCount = requests.length;
      expect(finalRequestCount - initialRequestCount).toBeLessThanOrEqual(1);
    });

    test('should handle large API responses efficiently', async ({ page }) => {
      await helpers.login();
      
      // Measure response time for large dataset
      const startTime = Date.now();
      
      await helpers.navigateTo('/projects');
      await helpers.waitForLoading();
      
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time
      expect(loadTime).toBeLessThan(5000);
      
      // Verify UI is responsive
      await expect(page.locator('[data-testid="projects-list"]')).toBeVisible();
      
      // Test scrolling performance
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      
      await page.waitForTimeout(100);
      
      // UI should remain responsive
      await expect(page.locator('[data-testid="projects-list"]')).toBeVisible();
    });
  });
});
