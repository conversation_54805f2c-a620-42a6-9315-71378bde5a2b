import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers.js';

test.describe('Critical User Journeys', () => {
  let helpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
  });

  test.describe('CEO Complete Workflow', () => {
    test('should complete full project lifecycle as CEO', async ({ page }) => {
      // 1. <PERSON><PERSON> as CEO
      await helpers.login();
      
      // 2. Review dashboard and current state
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Verify CEO can see overview
      await expect(page.locator('[data-testid="dashboard-stats"]')).toBeVisible();
      const initialProjectCount = await page.locator('[data-testid="projects-count"]').textContent();
      
      // 3. Create a new strategic project
      const projectData = await helpers.createProject({
        title: 'E-commerce Platform Redesign',
        description: 'Complete redesign of our e-commerce platform to improve user experience and increase conversions',
        requirements: 'Modern UI/UX, mobile-first design, improved checkout flow, performance optimization',
        priority: 'HIGH',
        estimatedDuration: '90',
        budget: '150000'
      });
      
      // 4. Navigate to project details and set up team
      await helpers.navigateTo('/projects');
      await page.click(`[data-testid="project-${projectData.title}"]`);
      
      // Verify project details are correct
      await expect(page.locator('[data-testid="project-title"]')).toContainText(projectData.title);
      await expect(page.locator('[data-testid="project-budget"]')).toContainText('150000');
      
      // 5. Assign team members to project
      await page.click('[data-testid="team-tab"]');
      
      // Assign Product Manager
      await page.click('[data-testid="assign-member-button"]');
      await page.selectOption('[data-testid="member-select"]', { label: /Product Manager/ });
      await page.selectOption('[data-testid="role-select"]', 'Lead');
      await page.click('[data-testid="confirm-assignment-button"]');
      
      // Assign Frontend Developer
      await page.click('[data-testid="assign-member-button"]');
      await page.selectOption('[data-testid="member-select"]', { label: /Frontend/ });
      await page.selectOption('[data-testid="role-select"]', 'Developer');
      await page.click('[data-testid="confirm-assignment-button"]');
      
      // Assign UI/UX Designer
      await page.click('[data-testid="assign-member-button"]');
      await page.selectOption('[data-testid="member-select"]', { label: /Designer/ });
      await page.selectOption('[data-testid="role-select"]', 'Designer');
      await page.click('[data-testid="confirm-assignment-button"]');
      
      // 6. Create initial project tasks
      await helpers.navigateTo('/tasks');
      
      const tasks = [
        {
          title: 'User Research and Analysis',
          description: 'Conduct user interviews and analyze current platform usage',
          priority: 'HIGH',
          estimatedHours: '40',
          assignee: 'Product Manager'
        },
        {
          title: 'UI/UX Design Mockups',
          description: 'Create wireframes and high-fidelity mockups for new design',
          priority: 'HIGH',
          estimatedHours: '60',
          assignee: 'UI/UX Designer'
        },
        {
          title: 'Frontend Implementation',
          description: 'Implement new design using modern frontend technologies',
          priority: 'MEDIUM',
          estimatedHours: '120',
          assignee: 'Frontend Developer'
        }
      ];
      
      for (const taskData of tasks) {
        await helpers.createTask(taskData);
      }
      
      // 7. Monitor project progress
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Verify project count increased
      const newProjectCount = await page.locator('[data-testid="projects-count"]').textContent();
      expect(parseInt(newProjectCount)).toBeGreaterThan(parseInt(initialProjectCount));
      
      // 8. Review team workload
      await helpers.navigateTo('/team');
      await helpers.waitForLoading();
      
      // Verify team members are visible and assigned
      await expect(page.locator('[data-testid="team-members-list"]')).toBeVisible();
      await expect(page.locator('[data-testid^="team-member-"]')).toHaveCountGreaterThan(0);
      
      // 9. Check for headcount requests if needed
      await helpers.navigateTo('/headcount-requests');
      await helpers.waitForLoading();
      
      // If there are pending requests, review them
      const requestCards = page.locator('[data-testid^="headcount-request-"]');
      const requestCount = await requestCards.count();
      
      if (requestCount > 0) {
        // Review first request
        await requestCards.first().click();
        await expect(page.locator('[data-testid="request-details"]')).toBeVisible();
        
        // CEO can approve or reject
        if (await helpers.isVisible('[data-testid="approve-request-button"]')) {
          await page.click('[data-testid="approve-request-button"]');
          await expect(page.locator('[data-testid="request-status"]')).toContainText('APPROVED');
        }
      }
      
      // 10. Final dashboard review
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Verify all key metrics are updated
      await expect(page.locator('[data-testid="projects-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="team-members-count"]')).toBeVisible();
      
      // Verify recent activity shows project creation
      if (await helpers.isVisible('[data-testid="activity-feed"]')) {
        await expect(page.locator('[data-testid="activity-feed"]')).toContainText('project');
      }
    });

    test('should handle project scaling and team expansion', async ({ page }) => {
      await helpers.login();
      
      // 1. Create a large project that requires team expansion
      const projectData = await helpers.createProject({
        title: 'Enterprise Software Suite',
        description: 'Comprehensive enterprise software solution with multiple modules',
        requirements: 'CRM, ERP, Analytics, Mobile apps, API integrations',
        priority: 'URGENT',
        estimatedDuration: '180',
        budget: '500000'
      });
      
      // 2. Navigate to team management
      await helpers.navigateTo('/team');
      
      // 3. Check current team capacity
      const currentMembers = await page.locator('[data-testid^="team-member-"]').count();
      
      // 4. Request additional headcount
      await page.click('[data-testid="request-headcount-button"]');
      await page.selectOption('[data-testid="role-select"]', 'Backend Developer');
      await page.fill('[data-testid="justification-input"]', 'Need additional backend developers for enterprise suite development');
      await page.selectOption('[data-testid="urgency-select"]', 'HIGH');
      await page.click('[data-testid="submit-request-button"]');
      
      // 5. Verify request was created
      await helpers.navigateTo('/headcount-requests');
      await expect(page.locator('[data-testid^="headcount-request-"]')).toHaveCountGreaterThan(0);
      
      // 6. As CEO, approve the request
      await page.click('[data-testid^="headcount-request-"]');
      await page.click('[data-testid="approve-request-button"]');
      await expect(page.locator('[data-testid="request-status"]')).toContainText('APPROVED');
      
      // 7. Create tasks for the expanded project
      await helpers.navigateTo('/tasks');
      
      const enterpriseTasks = [
        { title: 'CRM Module Development', priority: 'HIGH', estimatedHours: '200' },
        { title: 'ERP Integration', priority: 'HIGH', estimatedHours: '150' },
        { title: 'Analytics Dashboard', priority: 'MEDIUM', estimatedHours: '100' },
        { title: 'Mobile App Development', priority: 'MEDIUM', estimatedHours: '180' },
        { title: 'API Gateway Setup', priority: 'HIGH', estimatedHours: '80' }
      ];
      
      for (const task of enterpriseTasks) {
        await helpers.createTask(task);
      }
      
      // 8. Monitor overall progress
      await helpers.navigateTo('/');
      
      // Verify dashboard reflects the scaled project
      await expect(page.locator('[data-testid="projects-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="headcount-requests-count"]')).toBeVisible();
    });
  });

  test.describe('Team Collaboration Workflow', () => {
    test('should simulate team member task completion workflow', async ({ page }) => {
      await helpers.login();
      
      // 1. Create project and tasks as CEO
      const projectData = await helpers.createProject({
        title: 'Mobile App MVP',
        description: 'Minimum viable product for mobile application',
        priority: 'HIGH'
      });
      
      const taskData = await helpers.createTask({
        title: 'Design App Wireframes',
        description: 'Create initial wireframes for mobile app',
        priority: 'HIGH',
        estimatedHours: '24'
      });
      
      // 2. Assign task to team member
      await helpers.navigateTo('/tasks');
      await page.click(`[data-testid="task-${taskData.title}"]`);
      await page.selectOption('[data-testid="task-assignee-select"]', { index: 1 });
      await page.click('[data-testid="assign-task-button"]');
      
      // 3. Simulate task progress updates
      const statusProgression = [
        { status: 'IN_PROGRESS', progress: '25' },
        { status: 'IN_PROGRESS', progress: '50' },
        { status: 'REVIEW', progress: '90' },
        { status: 'DONE', progress: '100' }
      ];
      
      for (const update of statusProgression) {
        await page.selectOption('[data-testid="task-status-select"]', update.status);
        await page.fill('[data-testid="task-progress-input"]', update.progress);
        await page.click('[data-testid="update-task-button"]');
        
        // Add progress comment
        await page.fill('[data-testid="comment-input"]', `Task ${update.progress}% complete - ${update.status}`);
        await page.click('[data-testid="add-comment-button"]');
        
        // Wait for update to process
        await helpers.waitForLoading();
      }
      
      // 4. Verify task completion
      await expect(page.locator('[data-testid="task-status"]')).toContainText('DONE');
      await expect(page.locator('[data-testid="task-progress"]')).toContainText('100%');
      
      // 5. Check dashboard for updated metrics
      await helpers.navigateTo('/');
      await helpers.waitForLoading();
      
      // Verify activity feed shows task completion
      if (await helpers.isVisible('[data-testid="activity-feed"]')) {
        await expect(page.locator('[data-testid="activity-feed"]')).toContainText('completed');
      }
    });
  });

  test.describe('Error Handling and Recovery', () => {
    test('should handle network interruptions gracefully', async ({ page }) => {
      await helpers.login();
      
      // Start creating a project
      await helpers.navigateTo('/projects');
      await page.click('[data-testid="create-project-button"]');
      await page.fill('[data-testid="project-title-input"]', 'Network Test Project');
      await page.fill('[data-testid="project-description-input"]', 'Testing network resilience');
      
      // Simulate network failure during save
      await page.route('**/api/projects/', route => route.abort());
      await page.click('[data-testid="save-project-button"]');
      
      // Verify error handling
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      
      // Restore network and retry
      await page.unroute('**/api/projects/');
      await page.click('[data-testid="retry-button"]');
      
      // Verify successful save after retry
      await helpers.waitForApiResponse('/api/projects/', 'POST');
      await expect(page).toHaveURL('/projects');
    });

    test('should handle session expiration during workflow', async ({ page }) => {
      await helpers.login();
      
      // Start a workflow
      await helpers.navigateTo('/projects');
      
      // Simulate session expiration
      await page.evaluate(() => {
        localStorage.removeItem('token');
      });
      
      // Try to perform an action that requires authentication
      await page.click('[data-testid="create-project-button"]');
      
      // Should be redirected to login
      await expect(page).toHaveURL('/login');
      
      // Re-login and continue workflow
      await helpers.login();
      await helpers.navigateTo('/projects');
      await expect(page.locator('[data-testid="create-project-button"]')).toBeVisible();
    });
  });

  test.describe('Performance and Scalability', () => {
    test('should handle large datasets efficiently', async ({ page }) => {
      await helpers.login();
      
      // Navigate to projects with potentially large dataset
      await helpers.navigateTo('/projects');
      await helpers.waitForLoading();
      
      // Measure loading time
      const startTime = Date.now();
      await page.reload();
      await helpers.waitForLoading();
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time even with many projects
      expect(loadTime).toBeLessThan(10000); // 10 seconds max
      
      // Verify pagination or virtual scrolling works
      if (await helpers.isVisible('[data-testid="pagination"]')) {
        await page.click('[data-testid="next-page-button"]');
        await helpers.waitForLoading();
        await expect(page.locator('[data-testid="projects-list"]')).toBeVisible();
      }
    });
  });
});
