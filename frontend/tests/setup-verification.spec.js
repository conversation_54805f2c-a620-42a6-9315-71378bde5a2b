import { test, expect } from '@playwright/test';

test.describe('Setup Verification', () => {
  test('should verify frontend is accessible', async ({ page }) => {
    await page.goto('/');
    
    // Should either show login page or redirect to login
    const currentUrl = page.url();
    const isLoginPage = currentUrl.includes('/login') || currentUrl === 'http://localhost:5173/login';
    const hasLoginForm = await page.locator('form').count() > 0;
    
    expect(isLoginPage || hasLoginForm).toBeTruthy();
  });

  test('should verify backend API is accessible', async ({ request }) => {
    // Test if backend is running by hitting a public endpoint
    try {
      const response = await request.get('http://localhost:8000/api/auth/login/', {
        failOnStatusCode: false
      });
      
      // Should get a response (even if it's an error for GET on login endpoint)
      expect(response.status()).toBeDefined();
    } catch (error) {
      // If we can't reach the backend, fail the test
      throw new Error(`Backend not accessible: ${error.message}`);
    }
  });

  test('should verify proxy configuration', async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
    
    // Check if we can make a request through the proxy
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/auth/login/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'wrongpassword'
          })
        });
        return {
          status: res.status,
          ok: res.ok
        };
      } catch (error) {
        return {
          error: error.message
        };
      }
    });
    
    // Should get a response (even if authentication fails)
    expect(response.status).toBeDefined();
    expect(response.error).toBeUndefined();
  });
});
