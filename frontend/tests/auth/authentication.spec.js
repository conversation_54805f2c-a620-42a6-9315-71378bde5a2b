import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers.js';

test.describe('Authentication Flow', () => {
  let helpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
  });

  test.describe('User Registration', () => {
    test('should register a new user successfully', async ({ page }) => {
      const userData = await helpers.register();
      
      // Verify successful registration
      await expect(page).toHaveURL('/');
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
      
      // Verify user data is displayed
      await page.click('[data-testid="user-menu"]');
      await expect(page.locator('[data-testid="user-name"]')).toContainText(userData.firstName);
    });

    test('should show validation errors for invalid registration data', async ({ page }) => {
      await page.goto('/register');
      
      // Try to register with invalid email
      await page.fill('[data-testid="first-name-input"]', 'Test');
      await page.fill('[data-testid="last-name-input"]', 'User');
      await page.fill('[data-testid="email-input"]', 'invalid-email');
      await page.fill('[data-testid="company-name-input"]', 'Test Company');
      await page.fill('[data-testid="password-input"]', 'short');
      await page.fill('[data-testid="confirm-password-input"]', 'different');
      
      await page.click('[data-testid="register-button"]');
      
      // Check for validation errors
      await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
    });

    test('should prevent registration with existing email', async ({ page }) => {
      await page.goto('/register');
      
      // Try to register with existing email
      await page.fill('[data-testid="first-name-input"]', 'Test');
      await page.fill('[data-testid="last-name-input"]', 'User');
      await page.fill('[data-testid="email-input"]', '<EMAIL>'); // Existing email
      await page.fill('[data-testid="company-name-input"]', 'Test Company');
      await page.fill('[data-testid="password-input"]', 'testpass123');
      await page.fill('[data-testid="confirm-password-input"]', 'testpass123');
      
      await page.click('[data-testid="register-button"]');
      
      // Check for error message
      await expect(page.locator('[data-testid="registration-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="registration-error"]')).toContainText('email');
    });
  });

  test.describe('User Login', () => {
    test('should login with valid credentials', async ({ page }) => {
      await helpers.login();
      
      // Verify successful login
      await expect(page).toHaveURL('/');
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
      
      // Verify dashboard content is loaded
      await expect(page.locator('[data-testid="dashboard-stats"]')).toBeVisible();
    });

    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto('/login');
      
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrongpassword');
      await page.click('[data-testid="login-button"]');
      
      // Check for error message
      await expect(page.locator('[data-testid="login-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="login-error"]')).toContainText('Invalid');
    });

    test('should show validation errors for empty fields', async ({ page }) => {
      await page.goto('/login');
      
      await page.click('[data-testid="login-button"]');
      
      // Check for validation errors
      await expect(page.locator('[data-testid="email-required-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-required-error"]')).toBeVisible();
    });

    test('should redirect to login when accessing protected routes without authentication', async ({ page }) => {
      await page.goto('/projects');
      
      // Should be redirected to login
      await expect(page).toHaveURL('/login');
    });
  });

  test.describe('User Logout', () => {
    test('should logout successfully', async ({ page }) => {
      await helpers.login();
      await helpers.logout();
      
      // Verify logout
      await expect(page).toHaveURL('/login');
      
      // Try to access protected route - should redirect to login
      await page.goto('/dashboard');
      await expect(page).toHaveURL('/login');
    });
  });

  test.describe('Authentication State Management', () => {
    test('should persist authentication across page refreshes', async ({ page }) => {
      await helpers.login();
      
      // Refresh the page
      await page.reload();
      
      // Should still be authenticated
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
      await expect(page).toHaveURL('/');
    });

    test('should handle token expiration gracefully', async ({ page }) => {
      await helpers.login();
      
      // Simulate token expiration by clearing localStorage
      await page.evaluate(() => {
        localStorage.removeItem('token');
      });
      
      // Try to access a protected route
      await page.goto('/projects');
      
      // Should be redirected to login
      await expect(page).toHaveURL('/login');
    });

    test('should auto-redirect authenticated users from login page', async ({ page }) => {
      await helpers.login();
      
      // Try to access login page while authenticated
      await page.goto('/login');
      
      // Should be redirected to dashboard
      await expect(page).toHaveURL('/');
    });
  });

  test.describe('Profile Management', () => {
    test('should display user profile information', async ({ page }) => {
      await helpers.login();
      
      // Navigate to profile or user menu
      await page.click('[data-testid="user-menu"]');
      
      // Check if profile information is displayed
      await expect(page.locator('[data-testid="user-email"]')).toContainText('<EMAIL>');
      await expect(page.locator('[data-testid="user-company"]')).toBeVisible();
    });
  });

  test.describe('Navigation Guards', () => {
    test('should protect all authenticated routes', async ({ page }) => {
      const protectedRoutes = [
        '/',
        '/projects',
        '/tasks',
        '/team',
        '/headcount-requests'
      ];
      
      for (const route of protectedRoutes) {
        await page.goto(route);
        await expect(page).toHaveURL('/login');
      }
    });

    test('should allow access to public routes without authentication', async ({ page }) => {
      const publicRoutes = ['/login', '/register'];
      
      for (const route of publicRoutes) {
        await page.goto(route);
        await expect(page).toHaveURL(route);
      }
    });
  });
});
