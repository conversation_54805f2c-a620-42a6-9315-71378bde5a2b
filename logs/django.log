INFO 2025-07-06 10:44:47,764 autoreload 54204 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 10:49:29,107 autoreload 54773 8400735296 Watching for file changes with StatReloader
WARNING 2025-07-06 10:49:52,798 log 54773 6192869376 Unauthorized: /api/v1/projects/
WARNING 2025-07-06 10:49:52,798 basehttp 54773 6192869376 "GET /api/v1/projects/ HTTP/1.1" 401 58
INFO 2025-07-06 10:50:50,177 autoreload 54960 8400735296 Watching for file changes with StatReloader
ERROR 2025-07-06 10:51:08,057 log 54960 6191984640 Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/apps/authentication/views.py", line 40, in login_view
    token, created = Token.objects.get_or_create(user=user)
                     ^^^^^^^^^^^^^
AttributeError: type object 'Token' has no attribute 'objects'
ERROR 2025-07-06 10:51:08,058 basehttp 54960 6191984640 "POST /api/v1/auth/login/ HTTP/1.1" 500 97549
ERROR 2025-07-06 10:51:48,851 log 54960 6191984640 Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/apps/authentication/views.py", line 40, in login_view
    token, created = Token.objects.get_or_create(user=user)
                     ^^^^^^^^^^^^^
AttributeError: type object 'Token' has no attribute 'objects'
ERROR 2025-07-06 10:51:48,852 basehttp 54960 6191984640 "POST /api/v1/auth/login/ HTTP/1.1" 500 97549
INFO 2025-07-06 10:52:38,385 autoreload 55250 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 10:53:24,359 basehttp 55250 6192820224 "POST /api/v1/auth/login/ HTTP/1.1" 200 342
INFO 2025-07-06 10:53:32,944 basehttp 55250 6192820224 "GET /api/v1/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 10:53:41,039 basehttp 55250 6192820224 "GET /api/v1/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 11:32:35,366 autoreload 62265 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:33:19,434 basehttp 62265 6159593472 "GET /api/v1/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 11:33:32,108 basehttp 62265 6159593472 "POST /api/v1/ai/evaluate-team-capacity/ HTTP/1.1" 200 166
INFO 2025-07-06 11:35:47,524 autoreload 62928 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:36:25,195 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:36:29,650 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:39:22,068 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:39:57,230 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:40:03,954 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
WARNING 2025-07-06 11:46:22,030 log 66423 8400735296 Bad Request: /api/v1/auth/change-password/
WARNING 2025-07-06 11:46:23,210 log 66423 8400735296 Bad Request: /api/v1/auth/change-password/
WARNING 2025-07-06 11:46:23,600 log 66423 8400735296 Unauthorized: /api/v1/auth/profile/
WARNING 2025-07-06 11:46:24,399 log 66423 8400735296 Bad Request: /api/v1/auth/login/
WARNING 2025-07-06 11:46:24,789 log 66423 8400735296 Bad Request: /api/v1/auth/login/
WARNING 2025-07-06 11:46:25,594 log 66423 8400735296 Unauthorized: /api/v1/auth/logout/
WARNING 2025-07-06 11:46:25,791 log 66423 8400735296 Bad Request: /api/v1/auth/register/
WARNING 2025-07-06 11:46:25,987 log 66423 8400735296 Bad Request: /api/v1/auth/register/
INFO 2025-07-06 11:46:35,447 autoreload 66476 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:48:04,906 basehttp 66476 6202503168 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:48:27,413 autoreload 67282 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:52:22,130 autoreload 66476 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/backend/urls.py changed, reloading.
INFO 2025-07-06 11:52:22,552 autoreload 68597 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:53:45,542 autoreload 68959 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:54:27,424 basehttp 68959 6128726016 "POST /api/auth/login/ HTTP/1.1" 200 341
INFO 2025-07-06 11:55:12,668 basehttp 68959 6128726016 "POST /api/auth/register/ HTTP/1.1" 201 341
INFO 2025-07-06 11:55:18,608 basehttp 68959 6128726016 "POST /api/auth/login/ HTTP/1.1" 200 342
INFO 2025-07-06 11:55:18,685 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 11:55:18,698 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 11:55:18,706 basehttp 68959 6128726016 "GET /api/headcount-requests/ HTTP/1.1" 200 52
INFO 2025-07-06 11:55:18,713 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
INFO 2025-07-06 11:55:27,813 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 11:55:28,451 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
INFO 2025-07-06 11:55:28,453 basehttp 68959 6145552384 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 11:55:28,989 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 11:55:29,002 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 11:55:29,012 basehttp 68959 6128726016 "GET /api/headcount-requests/ HTTP/1.1" 200 52
INFO 2025-07-06 11:55:29,019 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
INFO 2025-07-06 11:55:29,460 basehttp 68959 6145552384 "GET /api/ai-roles/ HTTP/1.1" 200 2805
INFO 2025-07-06 11:55:29,461 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 11:55:31,196 basehttp 68959 6128726016 "GET /api/headcount-requests/ HTTP/1.1" 200 52
INFO 2025-07-06 11:55:32,922 basehttp 68959 6145552384 "GET /api/ai-roles/ HTTP/1.1" 200 2805
INFO 2025-07-06 11:55:32,922 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 11:55:33,672 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 11:55:33,684 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 11:55:33,693 basehttp 68959 6128726016 "GET /api/headcount-requests/ HTTP/1.1" 200 52
INFO 2025-07-06 11:55:33,707 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
WARNING 2025-07-06 11:59:45,329 log 68959 6128726016 Unauthorized: /api/projects/
WARNING 2025-07-06 11:59:45,332 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 401 27
INFO 2025-07-06 11:59:53,684 basehttp 68959 6128726016 "POST /api/auth/login/ HTTP/1.1" 200 341
INFO 2025-07-06 12:00:16,837 basehttp 68959 6128726016 "POST /api/auth/login/ HTTP/1.1" 200 341
INFO 2025-07-06 12:00:58,664 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 52
INFO 2025-07-06 12:01:07,737 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 12:01:16,574 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
INFO 2025-07-06 12:01:24,603 basehttp 68959 6128726016 "GET /api/ai-roles/ HTTP/1.1" 200 2805
INFO 2025-07-06 12:01:35,562 basehttp 68959 6128726016 "POST /api/projects/ HTTP/1.1" 201 380
INFO 2025-07-06 12:02:14,821 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 12:02:15,760 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
INFO 2025-07-06 12:02:15,763 basehttp 68959 6145552384 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 12:02:16,468 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 12:02:57,347 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 1425
INFO 2025-07-06 12:03:05,387 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 2090
INFO 2025-07-06 12:03:23,751 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 2193
INFO 2025-07-06 12:03:27,783 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
INFO 2025-07-06 12:03:27,788 basehttp 68959 6145552384 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 12:03:28,485 basehttp 68959 6145552384 "GET /api/ai-roles/ HTTP/1.1" 200 2805
INFO 2025-07-06 12:03:28,487 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 12:03:29,666 basehttp 68959 6128726016 "GET /api/tasks/ HTTP/1.1" 200 52
INFO 2025-07-06 12:03:29,668 basehttp 68959 6145552384 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 12:03:30,254 basehttp 68959 6128726016 "GET /api/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 12:03:30,697 basehttp 68959 6145552384 "GET /api/ai-roles/ HTTP/1.1" 200 2805
INFO 2025-07-06 12:03:30,697 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 12:03:43,871 basehttp 68959 6128726016 "GET /api/headcount-requests/ HTTP/1.1" 200 52
INFO 2025-07-06 12:03:44,563 basehttp 68959 6128726016 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 12:03:44,565 basehttp 68959 6145552384 "GET /api/ai-roles/ HTTP/1.1" 200 2805
INFO 2025-07-06 12:04:27,060 basehttp 68959 6128726016 "GET /api/auth/profile/ HTTP/1.1" 200 282
INFO 2025-07-06 12:04:27,071 basehttp 68959 6145552384 "GET /api/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 12:04:27,072 basehttp 68959 6162378752 "GET /api/ai-roles/ HTTP/1.1" 200 2805
INFO 2025-07-06 12:05:43,297 autoreload 68959 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/apps/projects/views.py changed, reloading.
INFO 2025-07-06 12:05:43,605 autoreload 70633 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 12:06:12,852 autoreload 70633 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/apps/projects/views.py changed, reloading.
INFO 2025-07-06 12:06:13,151 autoreload 70690 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 12:06:28,996 autoreload 70690 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/apps/projects/views.py changed, reloading.
INFO 2025-07-06 12:06:29,275 autoreload 70713 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 12:06:42,050 autoreload 70713 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/apps/projects/views.py changed, reloading.
INFO 2025-07-06 12:06:42,362 autoreload 70742 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 12:07:11,641 autoreload 70742 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/apps/tasks/views.py changed, reloading.
INFO 2025-07-06 12:07:12,020 autoreload 70784 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 12:07:41,292 autoreload 70784 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/apps/tasks/views.py changed, reloading.
INFO 2025-07-06 12:07:41,617 autoreload 70827 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 12:07:55,210 basehttp 70827 6123253760 "GET /api/projects/statistics/ HTTP/1.1" 200 276
INFO 2025-07-06 12:08:05,008 basehttp 70827 6123253760 "GET /api/tasks/statistics/ HTTP/1.1" 200 275
INFO 2025-07-06 12:08:12,873 basehttp 70827 6123253760 "GET /api/tasks/overdue/ HTTP/1.1" 200 2
WARNING 2025-07-06 12:08:23,145 log 70927 8400735296 Unauthorized: /api/projects/
